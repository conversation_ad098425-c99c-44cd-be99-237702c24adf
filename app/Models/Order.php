<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
class Order extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * 与模型关联的表
     *
     * @var string
     */
    protected $table = 'orders';

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'company_code',
        'order_code',
        'customer_code',
        'sales_account',
        'department_id',
        'order_date',
        'customer_order_number',
        'sales_type_code',
        'currency_code',
        'exchange_rate',
        'exchange_rate_base_code',
        'tax_type_code',
        'tax_rate',
        'trade_term_code',
        'receipt_payment_term_code',
        'invoice_type_code',
        'pricing_method_code',
        'create_account',
        'update_account',
        'status',
        'remark',
    ];

    /**
     * 隐藏的属性
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'deleted_at',
    ];

    /**
     * 应该转换为日期的属性
     *
     * @var array<int, string>
     */
    protected $dates = [
        'order_date',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * 获取订单的客户信息
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_code', 'customer_code');
    }

    /**
     * 获取订单的销售人员
     */
    public function salesPerson()
    {
        return $this->belongsTo(User::class, 'sales_account', 'account');
    }

    /**
     * 获取订单的部门
     */
    public function department()
    {
        return $this->belongsTo(Department::class, 'department_id', 'id');
    }

    /**
     * 获取订单的明细信息
     */
    public function details()
    {
        return $this->hasMany(OrderDetail::class, 'order_code', 'order_code');
    }

    /**
     * 获取订单的币种
     */
    public function currency()
    {
        return $this->belongsTo(Currency::class, 'currency_code', 'currency_code');
    }

    /**
     * 获取订单的汇率基准
     */
    public function exchangeRateBasis()
    {
        return $this->belongsTo(ExchangeRateBasis::class, 'exchange_rate_base_code', 'exchange_rate_base_code');
    }

    /**
     * 获取订单的税种
     */
    public function taxType()
    {
        return $this->belongsTo(TaxType::class, 'tax_type_code', 'tax_type_code');
    }

    /**
     * 获取订单的交易条件
     */
    public function tradeTerm()
    {
        return $this->belongsTo(TradeTerm::class, 'trade_term_code', 'trade_term_code');
    }

    /**
     * 获取订单的收款条件
     */
    public function receiptPaymentTerm()
    {
        return $this->belongsTo(ReceiptPaymentTerm::class, 'receipt_payment_term_code', 'receipt_payment_term_code');
    }

    /**
     * 获取订单的发票类型
     */
    public function invoiceType()
    {
        return $this->belongsTo(InvoiceType::class, 'invoice_type_code', 'invoice_type_code');
    }

    /**
     * 获取订单的取价方式
     */
    public function pricingMethod()
    {
        return $this->belongsTo(SalesPricingMethod::class, 'pricing_method_code', 'sales_pricing_method_code');
    }

    /**
     * 获取订单的销售类型
     */
    public function salesType()
    {
        return $this->belongsTo(SalesType::class, 'sales_type_code', 'sales_type_code');
    }

    /**
     * 获取创建用户
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'create_account', 'account');
    }

    /**
     * 获取更新用户
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'update_account', 'account');
    }

    /**
     * 生成订单编号
     *
     * @param string $companyCode 公司编码
     * @return string
     */
    public static function generateOrderCode(string $companyCode): string
    {
        $today = date('ymd');
        $latestOrder = self::where('company_code', $companyCode)
            ->where('order_code', 'like', "{$companyCode}{$today}%")
            ->orderBy('order_code', 'desc')
            ->first();

        if ($latestOrder) {
            $sequence = (int)substr($latestOrder->order_code, -4) + 1;
        } else {
            $sequence = 1;
        }
        
        return $companyCode . $today . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * 创建订单及其明细
     *
     * @param array $orderData 订单数据
     * @param array $detailsData 订单明细数据
     * @param string $userAccount 用户账号
     * @return array
     * @throws \Exception
     */
    public static function createOrderWithDetails(array $orderData, array $detailsData, string $userAccount): array
    {
        \DB::beginTransaction();
        
        try {
            // 生成订单编号
            $orderCode = self::generateOrderCode($orderData['company_code']);
            
            // 创建订单主记录
            $order = new self();
            $order->fill($orderData);
            $order->order_code = $orderCode;
            $order->create_account = $userAccount;
            $order->update_account = $userAccount;
            $order->status = 'N'; // 默认未审核状态
            $order->save();

            // 创建订单明细记录
            $orderItems = [];
            foreach ($detailsData as $index => $detail) {
                $orderDetail = OrderDetail::createOrderDetail(
                    $orderData['company_code'],
                    $orderCode,
                    $index + 1,
                    $detail,
                    $orderData['tax_rate'] ?? 0,
                    $orderData['tax_category'] ?? 'B',
                    $order->tax_type_code,
                    $userAccount
                );
                $orderItems[] = $orderDetail;
            }

            \DB::commit();
            
            return [
                'order' => $order,
                'details' => $orderItems
            ];
            
        } catch (\Exception $e) {
            \DB::rollBack();
            throw $e;
        }
    }

    /**
     * 更新订单及其明细
     *
     * @param string $orderCode 订单编号
     * @param array $orderData 订单数据
     * @param array $detailsData 订单明细数据
     * @param string $userAccount 用户账号
     * @return array
     * @throws \Exception
     */
    public static function updateOrderWithDetails(string $orderCode, array $orderData, array $detailsData, string $userAccount): array
    {
        \DB::beginTransaction();
        
        try {
            $order = self::where('order_code', $orderCode)->firstOrFail();
            
            // 检查订单状态，只有未审核的订单可以修改
            if ($order->status !== 'N') {
                throw new \Exception('只有未审核的订单可以修改');
            }
            
            // 更新订单主记录
            $order->fill($orderData);
            $order->update_account = $userAccount;
            $order->save();
            
            // 删除原有的订单明细
            OrderDetail::where('order_code', $orderCode)->delete();
            
            // 创建新的订单明细
            $orderItems = [];
            foreach ($detailsData as $index => $detail) {
                $orderDetail = OrderDetail::createOrderDetail(
                    $order->company_code,
                    $orderCode,
                    $index + 1,
                    $detail,
                    $orderData['tax_rate'] ?? 0,
                    $orderData['tax_category'] ?? 'B',
                    $order->tax_type_code,
                    $userAccount
                );
                $orderItems[] = $orderDetail;
            }

            \DB::commit();
            
            return [
                'order' => $order,
                'details' => $orderItems
            ];
            
        } catch (\Exception $e) {
            \DB::rollBack();
            throw $e;
        }
    }

    /**
     * 删除订单
     *
     * @param string $orderCode 订单编号
     * @return bool
     * @throws \Exception
     */
    public static function deleteOrder(string $orderCode): bool
    {
        \DB::beginTransaction();
        
        try {
            $order = self::where('order_code', $orderCode)->firstOrFail();
            
            // 检查订单状态，只有未审核的订单可以删除
            if ($order->status !== 'N') {
                throw new \Exception('只有未审核的订单可以删除');
            }
            
            // 删除订单明细
            OrderDetail::where('order_code', $orderCode)->delete();
            
            // 删除订单主记录
            $order->delete();
            
            \DB::commit();
            
            return true;
            
        } catch (\Exception $e) {
            \DB::rollBack();
            throw $e;
        }
    }

    /**
     * 审核订单
     *
     * @param string $orderCode 订单编号
     * @param string $userAccount 用户账号
     * @return bool
     * @throws \Exception
     */
    public static function approveOrder(string $orderCode, string $userAccount): bool
    {
        $order = self::where('order_code', $orderCode)->firstOrFail();
        
        if ($order->status !== 'N') {
            throw new \Exception('只有未审核的订单可以审核');
        }
        
        $order->status = 'Y';
        $order->update_account = $userAccount;
        $order->save();
        
        return true;
    }

    /**
     * 作废订单
     *
     * @param string $orderCode 订单编号
     * @param string $userAccount 用户账号
     * @return bool
     * @throws \Exception
     */
    public static function cancelOrder(string $orderCode, string $userAccount): bool
    {
        $order = self::where('order_code', $orderCode)->firstOrFail();
        
        // 检查订单状态，已结案不能作废
        if ($order->status === 'C') {
            throw new \Exception('已结案的订单不能作废');
        }
        
        $order->status = 'X';
        $order->update_account = $userAccount;
        $order->save();
        
        return true;
    }

    /**
     * 结案订单
     *
     * @param string $orderCode 订单编号
     * @param string $userAccount 用户账号
     * @return bool
     * @throws \Exception
     */
    public static function closeOrder(string $orderCode, string $userAccount): bool
    {
        $order = self::where('order_code', $orderCode)->firstOrFail();
        
        if ($order->status !== 'Y') {
            throw new \Exception('只有已审核的订单可以结案');
        }
        
        $order->status = 'C';
        $order->update_account = $userAccount;
        $order->save();
        
        return true;
    }

    /**
     * 获取订单列表
     */
    public static function getOrderList(
        string $companyCode = 'TB',
        string $startDate,
        string $endDate,
        array $departmentId,
        array $salesAccount,
        array $customerCode,
        ?string $status,
        int $page = 1, 
        int $perPage = 10, 
        ?string $sortField = 'order_date', 
        ?string $sortDirection = 'desc'
    ) {
        // 字段映射关系
        $dbFieldMap = [
            'order_code' => 'orders.order_code',
            'order_date' => 'orders.order_date',
            'customer_code' => 'orders.customer_code',
            'sales_account' => 'orders.sales_account',
            'department_id' => 'orders.department_id',
            'status' => 'orders.status',
        ];

        $query = Order::query()
            ->where('company_code', $companyCode)
            ->whereBetween('order_date', [$startDate, $endDate]);

        // 部门处理
        $query->when($departmentId, function ($q) use ($departmentId) {
            $q->whereIn('department_id', $departmentId);
        });

        // 销售人员处理
        $query->when($salesAccount, function ($q) use ($salesAccount) {
            $q->whereIn('sales_account', $salesAccount);
        });

        // 客户编码处理
        $query->when($customerCode, function ($q) use ($customerCode) {
            $q->whereIn('customer_code', $customerCode);
        });

        // 订单状态处理
        $query->when($status, function ($q) use ($status) {
            $q->where('status', $status);
        });
        
        // 排序逻辑
        if ($sortField && isset($dbFieldMap[$sortField])) {
            $query->orderBy($dbFieldMap[$sortField], $sortDirection);
        } else {
            // 默认排序
            $query->orderBy('order_date', 'desc');
        }

        return $query->paginate($perPage, ['*'], 'page', $page);
    }
}
