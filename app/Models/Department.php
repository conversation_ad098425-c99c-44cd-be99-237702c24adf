<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Department extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'parent_id',
        'description',
        'status',
        'order',
    ];

    /**
     * 获取上级部门
     */
    public function parent()
    {
        return $this->belongsTo(Department::class, 'parent_id');
    }

    /**
     * 获取子部门
     */
    public function children()
    {
        return $this->hasMany(Department::class, 'parent_id');
    }

    /**
     * 获取部门主管（多个）
     */
    public function managers()
    {
        return $this->belongsToMany(User::class, 'department_manager', 'department_id', 'user_id')
                    ->withTimestamps();
    }

    /**
     * 获取部门员工
     */
    public function employees()
    {
        return $this->hasMany(User::class, 'department_id');
    }

    /**
     * 获取部门列表
     *
     * @param string|null $search 搜索关键词
     * @param string|null $status 状态筛选
     * @param int $perPage 每页数量
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public static function getDepartmentList(?string $search = null, ?string $status = null, int $perPage = 50)
    {
        $query = self::query()
            ->with(['parent', 'managers'])
            ->orderBy('order', 'asc');

        // 搜索过滤
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // 状态过滤
        if ($status) {
            $query->where('status', $status);
        }

        return $query->paginate($perPage);
    }

    /**
     * 创建部门及其主管关联
     *
     * @param array $departmentData 部门数据
     * @param array $managerIds 主管ID数组
     * @return Department
     * @throws \Exception
     */
    public static function createDepartmentWithManagers(array $departmentData, array $managerIds = []): Department
    {
        try {
            // 创建部门
            $department = self::create($departmentData);

            // 关联主管
            if (!empty($managerIds)) {
                $department->managers()->attach($managerIds);
            }

            return $department;
            
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 更新部门及其主管关联
     *
     * @param string $departmentId 部门ID
     * @param array $departmentData 部门数据
     * @param array $managerIds 主管ID数组
     * @return Department
     * @throws \Exception
     */
    public static function updateDepartmentWithManagers(string $departmentId, array $departmentData, array $managerIds = []): Department
    {
        try {
            $department = self::findOrFail($departmentId);
            
            // 更新部门基本信息
            $department->update($departmentData);

            // 同步部门主管
            $department->managers()->sync($managerIds);

            return $department;
            
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 删除部门
     *
     * @param string $departmentId 部门ID
     * @return bool
     * @throws \Exception
     */
    public static function deleteDepartment(string $departmentId): bool
    {
        $department = self::findOrFail($departmentId);
        
        // 检查是否有子部门
        $hasChildren = self::where('parent_id', $department->id)->exists();
        
        if ($hasChildren) {
            throw new \Exception('无法删除存在子部门的部门');
        }
        
        // 检查是否有关联的员工
        $hasEmployees = User::where('department_id', $department->id)->exists();
        
        if ($hasEmployees) {
            throw new \Exception('无法删除存在员工的部门');
        }

        // 删除部门前先解除与主管的关联
        $department->managers()->detach();
        $department->delete();

        return true;
    }

    /**
     * 获取部门详情及其员工列表
     *
     * @param string $departmentId 部门ID
     * @param int $userPerPage 员工分页数量
     * @return array
     */
    public static function getDepartmentWithEmployees(string $departmentId, int $userPerPage = 10): array
    {
        $department = self::with(['parent', 'managers', 'children'])->findOrFail($departmentId);
        
        // 获取部门员工并分页
        $users = $department->employees()->paginate($userPerPage);
        
        return [
            'department' => $department,
            'users' => $users
        ];
    }

    /**
     * 获取带层级的部门列表
     *
     * @return array 返回带层级的部门列表
     */
    public static function getDepartmentsWithLevel(): array
    {
        // 获取所有部门
        $allDepartments = self::orderBy('order', 'asc')->get();

        // 构建部门树
        $departmentTree = [];
        $departmentMap = [];

        // 创建映射表
        foreach ($allDepartments as $department) {
            $departmentMap[$department->id] = [
                'id' => $department->id,
                'name' => $department->name,
                'parent_id' => $department->parent_id,
                'level' => 0,
                'children' => []
            ];
        }

        // 构建树结构
        foreach ($departmentMap as &$department) {
            if ($department['parent_id'] && isset($departmentMap[$department['parent_id']])) {
                $departmentMap[$department['parent_id']]['children'][] = &$department;
            } else {
                $departmentTree[] = &$department;
            }
        }

        // 计算层级并展平树结构
        $flatDepartments = [];
        self::flattenDepartmentTree($departmentTree, $flatDepartments);

        return $flatDepartments;
    }

    /**
     * 递归展平部门树并计算层级
     *
     * @param array $tree 部门树
     * @param array &$result 结果数组
     * @param int $level 当前层级
     */
    private static function flattenDepartmentTree($tree, &$result, $level = 0)
    {
        foreach ($tree as $department) {
            $department['level'] = $level;
            $children = $department['children'];
            unset($department['children']);

            $result[] = $department;

            if (!empty($children)) {
                self::flattenDepartmentTree($children, $result, $level + 1);
            }
        }
    }
}
