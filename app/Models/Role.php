<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Role extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'description',
    ];

    /**
     * 该角色拥有的所有权限
     */
    public function permissions()
    {
        return $this->belongsToMany(Permission::class);
    }

    /**
     * 该角色的所有用户
     */
    public function users()
    {
        return $this->belongsToMany(User::class);
    }

    /**
     * 获取角色列表
     *
     * @param string|null $search 搜索关键词
     * @param int $perPage 每页数量
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public static function getRoleList(?string $search = null, int $perPage = 20)
    {
        $query = self::query()
            ->withCount(['users', 'permissions'])
            ->when($search, function($query, $search) {
                $query->where(function($query) use ($search) {
                    $query->where('name', 'like', "%{$search}%")
                        ->orWhere('description', 'like', "%{$search}%");
                });
            });

        return $query->orderBy('id')->paginate($perPage);
    }

    /**
     * 创建角色及其权限关联
     *
     * @param array $roleData 角色数据
     * @param array $permissionIds 权限ID数组
     * @return Role
     * @throws \Exception
     */
    public static function createRoleWithPermissions(array $roleData, array $permissionIds = []): Role
    {
        DB::beginTransaction();
        
        try {
            $role = self::create([
                'name' => $roleData['name'],
                'description' => $roleData['description'] ?? null,
            ]);

            if (!empty($permissionIds)) {
                $role->permissions()->attach($permissionIds);
            }

            DB::commit();
            
            return $role;
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 更新角色及其权限关联
     *
     * @param string $roleId 角色ID
     * @param array $roleData 角色数据
     * @param array $permissionIds 权限ID数组
     * @return Role
     * @throws \Exception
     */
    public static function updateRoleWithPermissions(string $roleId, array $roleData, array $permissionIds = []): Role
    {
        DB::beginTransaction();
        
        try {
            $role = self::findOrFail($roleId);
            
            // 禁止更新管理员角色
            if ($role->name === 'admin') {
                throw new \Exception('管理员角色不可编辑');
            }
            
            $role->update([
                'name' => $roleData['name'],
                'description' => $roleData['description'] ?? null,
            ]);

            $role->permissions()->sync($permissionIds);

            DB::commit();
            
            return $role;
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 删除角色
     *
     * @param string $roleId 角色ID
     * @return bool
     * @throws \Exception
     */
    public static function deleteRole(string $roleId): bool
    {
        $role = self::findOrFail($roleId);
        
        // 禁止删除管理员角色
        if ($role->name === 'admin') {
            throw new \Exception('管理员角色不可删除');
        }
        
        // 检查是否有用户使用此角色
        if ($role->users()->count() > 0) {
            throw new \Exception('该角色下还有用户，无法删除');
        }
        
        // 删除角色前先删除与权限的关联
        $role->permissions()->detach();
        $role->delete();
        
        return true;
    }

    /**
     * 为角色分配用户
     *
     * @param string $roleId 角色ID
     * @param array $userIds 用户ID数组
     * @return bool
     * @throws \Exception
     */
    public static function assignUsers(string $roleId, array $userIds): bool
    {
        $role = self::findOrFail($roleId);
        
        // 验证用户是否存在
        $existingUserIds = User::whereIn('id', $userIds)->pluck('id')->toArray();
        $invalidUserIds = array_diff($userIds, $existingUserIds);
        
        if (!empty($invalidUserIds)) {
            throw new \Exception('用户ID无效: ' . implode(', ', $invalidUserIds));
        }
        
        $role->users()->sync($userIds);
        
        return true;
    }

    /**
     * 获取角色详情及其用户列表
     *
     * @param string $roleId 角色ID
     * @param int $userPerPage 用户分页数量
     * @return array
     */
    public static function getRoleWithUsers(string $roleId, int $userPerPage = 5): array
    {
        $role = self::with('permissions')->findOrFail($roleId);
        
        // 获取有此角色的用户
        $users = User::whereHas('roles', function($query) use ($role) {
                    $query->where('roles.id', $role->id);
                })
                ->leftJoin('departments', 'users.department_id', '=', 'departments.id')
                ->select('users.id', 'users.name', 'users.email', 'users.employee_id', 
                         'departments.name as department', 'users.position')
                ->paginate($userPerPage);

        return [
            'role' => $role,
            'users' => $users
        ];
    }
} 