<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Permission extends Model
{
    use HasFactory;

    /**
     * 与模型关联的表
     *
     * @var string
     */
    protected $table = 'permissions';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'description',
    ];

    /**
     * 拥有此权限的所有角色
     */
    public function roles()
    {
        return $this->belongsToMany(Role::class);
    }

    /**
     * 获取权限列表
     *
     * @param string|null $search 搜索关键词
     * @param int $perPage 每页数量
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public static function getPermissionList(?string $search = null, int $perPage = 20)
    {
        $query = self::query()
            ->withCount(['roles'])
            ->when($search, function($query, $search) {
                $query->where(function($query) use ($search) {
                    $query->where('name', 'like', "%{$search}%")
                        ->orWhere('description', 'like', "%{$search}%");
                });
            });

        return $query->orderBy('id')->paginate($perPage);
    }

    /**
     * 创建权限及其角色关联
     *
     * @param array $permissionData 权限数据
     * @param array $roleIds 角色ID数组
     * @return Permission
     * @throws \Exception
     */
    public static function createPermissionWithRoles(array $permissionData, array $roleIds = []): Permission
    {
        DB::beginTransaction();
        
        try {
            $permission = self::create([
                'name' => $permissionData['name'],
                'description' => $permissionData['description'] ?? null,
            ]);

            if (!empty($roleIds)) {
                $permission->roles()->attach($roleIds);
            }

            DB::commit();
            
            return $permission;
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 更新权限及其角色关联
     *
     * @param string $permissionId 权限ID
     * @param array $permissionData 权限数据
     * @param array $roleIds 角色ID数组
     * @return Permission
     * @throws \Exception
     */
    public static function updatePermissionWithRoles(string $permissionId, array $permissionData, array $roleIds = []): Permission
    {
        DB::beginTransaction();
        
        try {
            $permission = self::findOrFail($permissionId);
            
            $permission->update([
                'name' => $permissionData['name'],
                'description' => $permissionData['description'] ?? null,
            ]);

            $permission->roles()->sync($roleIds);

            DB::commit();
            
            return $permission;
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 删除权限
     *
     * @param string $permissionId 权限ID
     * @return bool
     * @throws \Exception
     */
    public static function deletePermission(string $permissionId): bool
    {
        $permission = self::findOrFail($permissionId);
        
        // 删除权限前先删除与角色的关联
        $permission->roles()->detach();
        $permission->delete();
        
        return true;
    }

    /**
     * 获取权限详情
     *
     * @param string $permissionId 权限ID
     * @return Permission
     */
    public static function getPermissionWithRoles(string $permissionId): Permission
    {
        return self::with('roles')->findOrFail($permissionId);
    }
} 