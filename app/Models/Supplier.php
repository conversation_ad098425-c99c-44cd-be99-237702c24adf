<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Supplier extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * 与模型关联的表
     *
     * @var string
     */
    protected $table = 'suppliers';

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'company_code',
        'code',
        'short_name',
        'full_name',
        'currency_id',
        'tax_type_id',
        'trade_term_id',
        'pricing_method_id',
        'invoice_type_id',
        'payment_term_id',
        'account_payable_type_id',
        'purchase_type_id',
        'exchange_rate_basis_id',
        'tax_number',
        'status',
        'remarks',
    ];

    /**
     * 隐藏的属性
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'deleted_at',
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * 币种关联
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    /**
     * 税种关联
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function taxType()
    {
        return $this->belongsTo(TaxType::class);
    }

    /**
     * 交易条件关联
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function tradeTerm()
    {
        return $this->belongsTo(TradeTerm::class);
    }

    /**
     * 取价方式关联
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function pricingMethod()
    {
        return $this->belongsTo(PurchasePricingMethod::class);
    }

    /**
     * 发票类型关联
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function invoiceType()
    {
        return $this->belongsTo(InvoiceType::class);
    }

    /**
     * 付款条件关联
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function paymentTerm()
    {
        return $this->belongsTo(ReceiptPaymentTerm::class);
    }

    /**
     * 应付账款类别关联
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function accountPayableType()
    {
        return $this->belongsTo(AccountReceivablePayableType::class);
    }

    /**
     * 采购分类关联
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function purchaseType()
    {
        return $this->belongsTo(PurchaseType::class);
    }

    /**
     * 汇率计算基准关联
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function exchangeRateBasis()
    {
        return $this->belongsTo(ExchangeRateBasis::class);
    }

    /**
     * 生成供应商编号
     * 
     * @return string
     */
    public static function generateSupplierCode()
    {
        $prefix = 'V';
        $latestSupplier = self::withTrashed()->orderBy('id', 'desc')->first();
        
        if (!$latestSupplier) {
            return $prefix . '000001';
        }
        
        $lastCode = $latestSupplier->code;
        if (preg_match('/^V(\d+)$/', $lastCode, $matches)) {
            $number = (int)$matches[1];
            $number++;
            return $prefix . str_pad($number, 6, '0', STR_PAD_LEFT);
        }
        
        return $prefix . '000001';
    }

    /**
     * 获取供应商列表
     *
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @param string|null $search 搜索关键词
     * @param string|null $sortField 排序字段
     * @param string $sortDirection 排序方向 (asc|desc)
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public static function getSupplierList(
        int $page = 1,
        int $perPage = 10,
        ?string $search = null,
        ?string $sortField = null,
        string $sortDirection = 'asc',
        ?string $status = null
    ) {
        // 映射前端排序字段 => 数据库字段
        $dbFieldMap = [
            'code' => 'code',
            'short_name' => 'short_name',
            'full_name' => 'full_name',
            'currency' => 'currency_id',
            'tax_type' => 'tax_type_id',
            'trade_term' => 'trade_term_id',
            'pricing_method' => 'pricing_method_id',
            'invoice_type' => 'invoice_type_id',
            'payment_term' => 'payment_term_id',
            'account_payable_type' => 'account_payable_type_id',
            'purchase_type' => 'purchase_type_id',
            'exchange_rate_basis' => 'exchange_rate_basis_id',
        ];
        
        // 验证排序方向
        if (!in_array(strtolower($sortDirection), ['asc', 'desc'])) {
            $sortDirection = 'asc';
        }
        
        $query = Supplier::query();

        // 搜索条件
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('code', 'like', "%{$search}%")
                  ->orWhere('short_name', 'like', "%{$search}%")
                  ->orWhere('full_name', 'like', "%{$search}%");
            });
        }

        if ($status) {
            $query->where('status', $status);
        }

        if ($sortField) {
            $field = $dbFieldMap[$sortField] ?? 'created_at';
            $query->orderBy($field, $sortDirection);
        }

        try {
            $suppliers = $query->orderBy('short_name', 'desc')->paginate($perPage, ['*'], 'page', $page);

            return $suppliers;
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }   
    }

    /**
     * 创建供应商
     *
     * @param array $supplierData 供应商数据
     * @return Supplier
     * @throws \Exception
     */
    public static function createSupplier(array $supplierData): Supplier
    {
        try {
            // 生成供应商编号
            $supplierData['code'] = self::generateSupplierCode();
            
            // 创建供应商
            $supplier = self::create($supplierData);
            
            return $supplier;
            
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 更新供应商信息
     *
     * @param string $supplierId 供应商ID
     * @param array $supplierData 供应商数据
     * @return Supplier
     * @throws \Exception
     */
    public static function updateSupplier(string $supplierId, array $supplierData): Supplier
    {
        try {
            $supplier = self::findOrFail($supplierId);
            
            // 供应商编号不允许修改，排除code字段
            unset($supplierData['code']);
            
            $supplier->update($supplierData);
            
            return $supplier->fresh();
            
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 删除供应商（软删除）
     *
     * @param string $supplierId 供应商ID
     * @return bool
     * @throws \Exception
     */
    public static function deleteSupplier(string $supplierId): bool
    {
        try {
            $supplier = self::findOrFail($supplierId);
            $supplier->delete();
            
            return true;
            
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 获取供应商详情
     *
     * @param string $supplierId 供应商ID
     * @return Supplier
     * @throws \Exception
     */
    public static function getSupplierWithRelations(string $supplierId): Supplier
    {
        return self::with([
            'currency',
            'taxType',
            'tradeTerm',
            'pricingMethod',
            'invoiceType',
            'paymentTerm',
            'accountPayableType',
            'purchaseType',
            'exchangeRateBasis'
        ])->findOrFail($supplierId);
    }
} 