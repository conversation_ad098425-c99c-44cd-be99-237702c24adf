<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Fortify\TwoFactorAuthenticatable;
use <PERSON><PERSON>\Jetstream\HasProfilePhoto;
// use Laravel\Jetstream\HasTeams;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens;

    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory;
    use HasProfilePhoto;
    // use HasTeams;
    use Notifiable;
    use TwoFactorAuthenticatable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'account',
        'locale',
        'default_company_code',
        'name',
        'email',
        'password',
        'employee_id',
        'department_id',
        'position',
        'phone',
        'address',
        'hire_date',
        'status',
        'emergency_contact',
        'emergency_phone',
        'notes',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = [
        'profile_photo_url',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'hire_date' => 'date',
    ];

    /**
     * 用户拥有的所有角色
     */
    public function roles()
    {
        return $this->belongsToMany(Role::class);
    }

    /**
     * 判断用户是否有指定角色
     *
     * @param string|array $roles 角色名称或角色名称数组
     * @return bool
     */
    public function hasRole($roles)
    {
        if (is_string($roles)) {
            return $this->roles->contains('name', $roles);
        }

        return (bool) $this->roles->pluck('name')->intersect($roles)->count();
    }

    /**
     * 判断用户是否有指定权限
     *
     * @param string|array $permissions 权限名称或权限名称数组
     * @return bool
     */
    public function hasPermission($permissions)
    {
        $allPermissions = $this->getAllPermissions();

        if (is_string($permissions)) {
            return $allPermissions->contains('name', $permissions);
        }

        return (bool) $allPermissions->pluck('name')->intersect($permissions)->count();
    }

    /**
     * 获取用户所有权限
     *
     * @return \Illuminate\Support\Collection
     */
    public function getAllPermissions()
    {
        return $this->roles->flatMap(function ($role) {
            return $role->permissions;
        })->unique('id');
    }

    /**
     * 获取用户所属部门
     */
    public function department()
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * 获取用户作为主管管理的部门
     */
    public function managedDepartments()
    {
        return $this->belongsToMany(Department::class, 'department_manager', 'user_id', 'department_id')
                    ->withTimestamps();
    }

    /**
     * 获取用户的默认公司
     */
    public function defaultCompany()
    {
        return $this->belongsTo(Company::class, 'default_company_code', 'company_code');
    }

    /**
     * 获取用户关联的公司
     */
    public function companies()
    {
        return $this->belongsToMany( Company::class, 'user_company', 'user_id',  'company_code')
            ->withTimestamps();
    }

    /**
     * 获取用户语言
     */
    public function locale()
    {
        return $this->belongsTo(Locale::class, 'locale', 'locale');
    }

    /**
     * 获取用户列表
     *
     * @param string|null $search 搜索关键词
     * @param string|null $status 状态筛选
     * @param string|null $departmentId 部门筛选
     * @param int $perPage 每页数量
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public static function getUserList(?string $search = null, ?string $status = null, ?string $departmentId = null, int $perPage = 20)
    {
        $query = self::query()
            ->leftJoin('departments', 'users.department_id', '=', 'departments.id')
            ->select('users.*', 'departments.name as department')
            ->with('companies')
            ->when($search, function ($query, $search) {
                $query->where(function ($query) use ($search) {
                    $query->where('users.name', 'like', "%{$search}%")
                        ->orWhere('users.account', 'like', "%{$search}%")
                        ->orWhere('users.employee_id', 'like', "%{$search}%")
                        ->orWhere('users.phone', 'like', "%{$search}%");
                });
            })
            ->when($status, function ($query, $status) {
                $query->where('users.status', $status);
            })
            ->when($departmentId, function ($query, $departmentId) {
                $query->where('users.department_id', $departmentId);
            });

        return $query->orderBy('employee_id')->paginate($perPage);
    }

    /**
     * 创建用户及其关联
     *
     * @param array $userData 用户数据
     * @param array $companyIds 公司ID数组
     * @param array $roleIds 角色ID数组
     * @return User
     * @throws \Exception
     */
    public static function createUserWithRelations(array $userData, array $companyIds = [], array $roleIds = []): User
    {
        \DB::beginTransaction();
        
        try {
            // 确保默认公司包含在授权公司列表中
            if (!in_array($userData['default_company_code'], $companyIds)) {
                $companyIds[] = $userData['default_company_code'];
            }

            // 创建用户
            $user = self::create($userData);

            // 关联公司
            if (!empty($companyIds)) {
                $user->companies()->attach($companyIds);
            }

            // 关联角色
            if (!empty($roleIds)) {
                $user->roles()->attach($roleIds);
            }

            \DB::commit();
            
            return $user;
            
        } catch (\Exception $e) {
            \DB::rollBack();
            throw $e;
        }
    }

    /**
     * 更新用户及其关联
     *
     * @param string $userId 用户ID
     * @param array $userData 用户数据
     * @param array $companyIds 公司ID数组
     * @param array $roleIds 角色ID数组
     * @return User
     * @throws \Exception
     */
    public static function updateUserWithRelations(string $userId, array $userData, array $companyIds = [], array $roleIds = []): User
    {
        \DB::beginTransaction();
        
        try {
            $user = self::findOrFail($userId);
            
            // 确保默认公司包含在授权公司列表中
            if (isset($userData['default_company_code']) && !in_array($userData['default_company_code'], $companyIds)) {
                $companyIds[] = $userData['default_company_code'];
            }

            // 更新用户基本信息
            $user->update($userData);

            // 更新授权公司
            $user->companies()->sync($companyIds);

            // 更新角色
            $user->roles()->sync($roleIds);

            \DB::commit();
            
            return $user;
            
        } catch (\Exception $e) {
            \DB::rollBack();
            throw $e;
        }
    }

    /**
     * 删除用户
     *
     * @param string $userId 用户ID
     * @param string $currentUserId 当前登录用户ID
     * @return bool
     * @throws \Exception
     */
    public static function deleteUser(string $userId, string $currentUserId): bool
    {
        // 确保不会删除当前登录用户
        if ($currentUserId === $userId) {
            throw new \Exception('无法删除当前登录用户');
        }

        $user = self::findOrFail($userId);

        // 删除用户前先删除与角色的关联
        $user->roles()->detach();
        $user->companies()->detach();
        $user->delete();

        return true;
    }

    /**
     * 获取用户详情及其关联信息
     *
     * @param string $userId 用户ID
     * @return User
     */
    public static function getUserWithRelations(string $userId): User
    {
        return self::with([
            'department', 
            'companies' => function($query) {
                $query->join('company_translations', 'companies.company_code', '=', 'company_translations.company_code')
                      ->where('company_translations.locale', \Auth::user()->locale)
                      ->select('companies.company_code', 'company_translations.company_name');
            },
            'roles'
        ])->findOrFail($userId);
    }

    /**
     * 获取用户的公司列表
     *
     * @param string $userId 用户ID
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getUserCompanies(string $userId)
    {
        $user = self::findOrFail($userId);
        
        return $user->companies()
            ->join('company_translations', 'companies.company_code', '=', 'company_translations.company_code')
            ->where('company_translations.locale', $user->locale)
            ->orderBy('companies.company_code')
            ->get(['companies.company_code', 'company_translations.company_name']);
    }

    /**
     * 获取简单用户列表
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getSimpleUserList()
    {
        return self::select('id', 'name', 'email', 'department_id', 'position')
            ->orderBy('name')
            ->get();
    }
}
