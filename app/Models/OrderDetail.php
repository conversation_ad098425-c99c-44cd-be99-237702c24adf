<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
class OrderDetail extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * 与模型关联的表
     *
     * @var string
     */
    protected $table = 'order_details';

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'company_code',
        'order_code',
        'order_item',
        'material_code',
        'quantity',
        'production_quantity',
        'unit_price',
        'standard_unit_price',
        'tax_type_code',
        'tax_rate',
        'tax_amount',
        'total_price_tax',
        'total_price_no_tax',
        'clear_table_number',
        'customer_material_code',
        'customer_material_specification',
        'color',
        'brand',
        'description',
        'inner_packing',
        'outer_packing',
        'warning',
        'borrow',
        'expected_completion_date',
        'expected_delivery_date',
        'actual_completion_date',
        'actual_delivery_date',
        'order_price_id',
        'order_price_item',
        'create_account',
        'update_account',
        'status',
        'remark',
        'remark_2',
    ];

    /**
     * 隐藏的属性
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'deleted_at',
    ];

    /**
     * 应该转换为日期的属性
     *
     * @var array<int, string>
     */
    protected $dates = [
        'expected_completion_date',
        'expected_delivery_date',
        'actual_completion_date',
        'actual_delivery_date',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * 获取订单明细所属的订单
     */
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_code', 'order_code');
    }

    /**
     * 获取订单明细的物料信息
     */
    public function material()
    {
        return $this->belongsTo(Material::class, 'material_code', 'material_code');
    }

    /**
     * 获取订单明细的税种
     */
    public function taxType()
    {
        return $this->belongsTo(TaxType::class, 'tax_type_code', 'tax_type_code');
    }

    /**
     * 获取订单明细的颜色
     */
    public function color()
    {
        return $this->belongsTo(Colors::class, 'color_code', 'color_code');
    }

    /**
     * 获取创建用户
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'create_account', 'account');
    }

    /**
     * 获取更新用户
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'update_account', 'account');
    }

    /**
     * 计算订单明细的各种金额
     *
     * @param float $quantity 数量
     * @param float $unitPrice 单价
     * @param float $taxRate 税率
     * @param string $taxCategory 税种分类 A:零税 B:含税 C:不含税
     * @return array
     */
    public static function calculateDetailAmounts(float $quantity, float $unitPrice, float $taxRate, string $taxCategory): array
    {
        switch ($taxCategory) {
            case 'A': // 零税
                $totalPriceNoTax = $quantity * $unitPrice;
                $totalPriceTax = $totalPriceNoTax;
                $taxAmount = 0;
                break;
            
            case 'B': // 含税
                $totalPriceTax = $quantity * $unitPrice;
                $totalPriceNoTax = $totalPriceTax / (1 + $taxRate / 100);
                $taxAmount = $totalPriceTax - $totalPriceNoTax;
                break;
            
            case 'C': // 不含税
                $totalPriceNoTax = $quantity * $unitPrice;
                $taxAmount = $totalPriceNoTax * ($taxRate / 100);
                $totalPriceTax = $totalPriceNoTax + $taxAmount;
                break;
            
            default: // 默认含税
                $totalPriceTax = $quantity * $unitPrice;
                $totalPriceNoTax = $totalPriceTax / (1 + $taxRate / 100);
                $taxAmount = $totalPriceTax - $totalPriceNoTax;
                break;
        }

        return [
            'total_price_no_tax' => round($totalPriceNoTax, 4),
            'total_price_tax' => round($totalPriceTax, 4),
            'tax_amount' => round($taxAmount, 4)
        ];
    }

    /**
     * 创建订单明细
     *
     * @param string $companyCode 公司编码
     * @param string $orderCode 订单编号
     * @param int $orderItem 项次
     * @param array $detailData 明细数据
     * @param float $defaultTaxRate 默认税率
     * @param string $taxCategory 税种分类
     * @param string $taxTypeCode 税种编码
     * @param string $userAccount 用户账号
     * @return OrderDetail
     */
    public static function createOrderDetail(
        string $companyCode,
        string $orderCode,
        int $orderItem,
        array $detailData,
        float $defaultTaxRate,
        string $taxCategory,
        string $taxTypeCode,
        string $userAccount
    ): OrderDetail {
        // 获取税率，优先使用明细的税率，否则使用订单的税率
        $taxRate = isset($detailData['tax_rate']) ? (float)$detailData['tax_rate'] : $defaultTaxRate;
        
        $unitPrice = (float)$detailData['unit_price'];
        $quantity = (float)$detailData['quantity'];
        $productionQuantity = isset($detailData['production_quantity']) ? (float)$detailData['production_quantity'] : $quantity;
        
        // 计算金额
        $amounts = self::calculateDetailAmounts($quantity, $unitPrice, $taxRate, $taxCategory);

        $orderDetail = new self();
        $orderDetail->company_code = $companyCode;
        $orderDetail->order_code = $orderCode;
        $orderDetail->order_item = $orderItem;
        $orderDetail->material_code = $detailData['material_code'];
        $orderDetail->quantity = $quantity;
        $orderDetail->production_quantity = $productionQuantity;
        $orderDetail->unit_price = $unitPrice;
        $orderDetail->standard_unit_price = 0; // 默认标准单价为0
        $orderDetail->tax_type_code = $detailData['tax_type_code'] ?? $taxTypeCode;
        $orderDetail->tax_rate = $taxRate;
        $orderDetail->total_price_no_tax = $amounts['total_price_no_tax'];
        $orderDetail->total_price_tax = $amounts['total_price_tax'];
        $orderDetail->tax_amount = $amounts['tax_amount'];
        $orderDetail->expected_completion_date = !empty($detailData['expected_completion_date']) ? $detailData['expected_completion_date'] : null;
        $orderDetail->expected_delivery_date = !empty($detailData['expected_delivery_date']) ? $detailData['expected_delivery_date'] : null;
        $orderDetail->color = $detailData['color'] ?? '';
        $orderDetail->brand = $detailData['brand'] ?? '';
        $orderDetail->description = $detailData['description'] ?? '';
        $orderDetail->inner_packing = $detailData['inner_packing'] ?? '';
        $orderDetail->outer_packing = $detailData['outer_packing'] ?? '';
        $orderDetail->warning = $detailData['warning'] ?? '';
        $orderDetail->borrow = $detailData['borrow'] ?? '';
        $orderDetail->clear_table_number = $detailData['clear_table_number'] ?? '';
        $orderDetail->customer_material_code = $detailData['customer_material_code'] ?? '';
        $orderDetail->customer_material_specification = $detailData['customer_material_specification'] ?? '';
        $orderDetail->create_account = $userAccount;
        $orderDetail->update_account = $userAccount;
        $orderDetail->status = '1'; // 默认一般状态
        $orderDetail->remark = $detailData['remark'] ?? '';
        $orderDetail->remark_2 = $detailData['remark_2'] ?? '';
        $orderDetail->save();
        
        return $orderDetail;
    }
}
