<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Customer extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * 与模型关联的表
     *
     * @var string
     */
    protected $table = 'customers';

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'company_code',
        'code',
        'short_name',
        'full_name',
        'currency_id',
        'tax_type_id',
        'trade_term_id',
        'pricing_method_id',
        'invoice_type_id',
        'payment_term_id',
        'account_receivable_type_id',
        'sales_type_id',
        'exchange_rate_basis_id',
        'tax_number',
        'status',
        'activity_level',
        'remarks',
    ];

    /**
     * 隐藏的属性
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'deleted_at',
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * 币种关联
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    /**
     * 税种关联
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function taxType()
    {
        return $this->belongsTo(TaxType::class);
    }

    /**
     * 交易条件关联
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function tradeTerm()
    {
        return $this->belongsTo(TradeTerm::class);
    }

    /**
     * 取价方式关联
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function pricingMethod()
    {
        return $this->belongsTo(SalesPricingMethod::class);
    }

    /**
     * 发票类型关联
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function invoiceType()
    {
        return $this->belongsTo(InvoiceType::class);
    }

    /**
     * 收款条件关联
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function paymentTerm()
    {
        return $this->belongsTo(ReceiptPaymentTerm::class);
    }

    /**
     * 应收账款类别关联
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function accountReceivableType()
    {
        return $this->belongsTo(AccountReceivablePayableType::class);
    }

    /**
     * 销售分类关联
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function salesType()
    {
        return $this->belongsTo(SalesType::class);
    }

    /**
     * 汇率计算基准关联
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function exchangeRateBasis()
    {
        return $this->belongsTo(ExchangeRateBasis::class);
    }

    /**
     * 生成客户编号
     * 
     * @return string
     */
    public static function generateCustomerCode()
    {
        $prefix = 'C';
        $latestCustomer = self::withTrashed()->orderBy('id', 'desc')->first();
        
        if (!$latestCustomer) {
            return $prefix . '00001';
        }
        
        $lastCode = $latestCustomer->code;
        if (preg_match('/^C(\d+)$/', $lastCode, $matches)) {
            $number = (int)$matches[1];
            $number++;
            return $prefix . str_pad($number, 5, '0', STR_PAD_LEFT);
        }
        
        return $prefix . '00001';
    }

    /**
     * 获取客户列表
     *
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @param string|null $search 搜索关键词
     * @param string|null $sortField 排序字段
     * @param string $sortDirection 排序方向 (asc|desc)
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public static function getCustomerList(
        string $companyCode,
        int $page = 1,
        int $perPage = 10,
        ?string $search = null,
        ?string $sortField = null,
        string $sortDirection = 'asc',
        ?string $status = null
    ) {
        // 映射前端排序字段 => 数据库字段
        $dbFieldMap = [
            'code' => 'code',
            'short_name' => 'short_name',
            'full_name' => 'full_name',
            'currency' => 'currency_id',
            'tax_type' => 'tax_type_id',
            'trade_term' => 'trade_term_id',
            'pricing_method' => 'pricing_method_id',
            'invoice_type' => 'invoice_type_id',
            'payment_term' => 'payment_term_id',
            'account_receivable_type' => 'account_receivable_type_id',
            'sales_type' => 'sales_type_id',
            'exchange_rate_basis' => 'exchange_rate_basis_id',
        ];
        
        // 验证排序方向
        if (!in_array(strtolower($sortDirection), ['asc', 'desc'])) {
            $sortDirection = 'asc';
        }
        
        $query = Customer::query()
            ->where('company_code', $companyCode);

        // 搜索条件
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('code', 'like', "%{$search}%")
                  ->orWhere('short_name', 'like', "%{$search}%")
                  ->orWhere('full_name', 'like', "%{$search}%");
            });
        }

        if ($status) {
            $query->where('status', $status);
        }

        if ($sortField) {
            $field = $dbFieldMap[$sortField] ?? 'created_at';
            $query->orderBy($field, $sortDirection);
        } else {
            $query->orderBy('code', 'asc');
        }

        try {
            $customers = $query->paginate($perPage, ['*'], 'page', $page);

            return $customers;
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }   
    }

    /**
     * 获取客户默认信息
     *
     * @param string $customerCode 客户编码
     * @return array
     * @throws \Exception
     */
    public static function getCustomerDefaults(string $customerCode): array
    {
        $customer = self::with([
            'currency',
            'taxType',
            'tradeTerm',
            'pricingMethod',
            'invoiceType',
            'paymentTerm',
            'salesType',
            'exchangeRateBasis'
        ])->where('code', $customerCode)->firstOrFail();

        return [
            'sales_type_code' => $customer->salesType ? $customer->salesType->code : null,
            'currency_code' => $customer->currency ? $customer->currency->code : null,
            'exchange_rate_base_code' => $customer->exchangeRateBasis ? $customer->exchangeRateBasis->id : null,
            'tax_type_code' => $customer->taxType ? $customer->taxType->code : null,
            'tax_rate' => $customer->taxType ? $customer->taxType->rate : 0,
            'trade_term_code' => $customer->tradeTerm ? $customer->tradeTerm->code : null,
            'receipt_payment_term_code' => $customer->paymentTerm ? $customer->paymentTerm->code : null,
            'invoice_type_code' => $customer->invoiceType ? $customer->invoiceType->code : null,
            'pricing_method_code' => $customer->pricingMethod ? $customer->pricingMethod->code : null,
            'invoice_title' => $customer->invoice_title ?? '',
            'invoice_content' => $customer->invoice_content ?? '',
            'invoice_address' => $customer->invoice_address ?? '',
            'invoice_phone' => $customer->invoice_phone ?? '',
            'invoice_bank' => $customer->invoice_bank ?? ''
        ];
    }

    /**
     * 创建客户
     *
     * @param array $customerData 客户数据
     * @return Customer
     * @throws \Exception
     */
    public static function createCustomer(array $customerData): Customer
    {
        try {
            // 生成客户编号
            $customerData['code'] = self::generateCustomerCode();
            
            // 创建客户
            $customer = self::create($customerData);
            
            return $customer;
            
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 更新客户信息
     *
     * @param string $customerId 客户ID
     * @param array $customerData 客户数据
     * @return Customer
     * @throws \Exception
     */
    public static function updateCustomer(string $customerId, array $customerData): Customer
    {
        try {
            $customer = self::findOrFail($customerId);
            
            // 客户编号不允许修改，排除code字段
            unset($customerData['code'], $customerData['activity_level']);
            
            $customer->update($customerData);
            
            return $customer->fresh();
            
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 删除客户（软删除）
     *
     * @param string $customerId 客户ID
     * @return bool
     * @throws \Exception
     */
    public static function deleteCustomer(string $customerId): bool
    {
        try {
            $customer = self::findOrFail($customerId);
            $customer->delete();
            
            return true;
            
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 获取客户详情
     *
     * @param string $customerId 客户ID
     * @return Customer
     * @throws \Exception
     */
    public static function getCustomerWithRelations(string $customerId): Customer
    {
        return self::with([
            'currency',
            'taxType',
            'tradeTerm',
            'pricingMethod',
            'invoiceType',
            'paymentTerm',
            'accountReceivableType',
            'salesType',
            'exchangeRateBasis'
        ])->findOrFail($customerId);
    }

    /**
     * 获取简单客户列表
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getSimpleCustomerList()
    {
        return self::where('status', 'Y')->get();
    }
}
