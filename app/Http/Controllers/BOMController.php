<?php

namespace App\Http\Controllers;

use App\Models\BOM;
use Auth;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BOMController extends Controller
{
    /**
     * 获取BOM列表
     *
     * @param Request $request 请求对象
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        // 获取分页参数
        $page = (int)$request->input('page', 1);
        $perPage = (int)$request->input('per_page', 10);
        $search = $request->input('search');
        
        // 获取排序参数
        $sortField = $request->input('sort_field');
        $sortDirection = $request->input('sort_direction', 'asc');
        
        // 获取产品分类筛选参数
        $categories = $request->input('categories');
        if ($categories && is_string($categories)) {
            $categories = explode(',', $categories);
        }
        
        $user = Auth::user();
        // 获取公司筛选参数
        $companyCode = $request->input('company_code');
        if (empty($companyCode)) {
            // 如果公司编码为空，则使用当前用户默认公司编码
            $companyCode = $user->default_company_code;
        } else {
            // 校验公司编码是否在授权公司列表中
            if (!$user->companies->contains('company_code', $companyCode)) {
                throw new \Exception('Company code is not authorized.');
            }
        }
        
        // 验证排序方向
        if (!in_array(strtolower($sortDirection), ['asc', 'desc'])) {
            $sortDirection = 'asc';
        }
        
        // 限制每页数量在10-100之间
        $perPage = max(10, min(100, $perPage));

        try {
            $BOMs = BOM::getBOMList($companyCode, $page, $perPage, $search, $sortField, $sortDirection, $categories);

            return response()->json([
                'status' => 'success',
                'data' => $BOMs->items(),
                'pagination' => [
                    'total' => $BOMs->total(),
                    'per_page' => $BOMs->perPage(),
                    'current_page' => $BOMs->currentPage(),
                    'last_page' => $BOMs->lastPage(),
                    'from' => $BOMs->firstItem() ?? 0,
                    'to' => $BOMs->lastItem() ?? 0,
                ],
                'sort' => [
                    'field' => $sortField,
                    'direction' => $sortDirection
                ],
                'categories' => $categories,
                'company_code' => $companyCode
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }

    /**
     * 获取产品分类
     * 
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function getProductCategory(Request $request)
    {
        try {
            // 从请求参数获取公司编码，如果没有提供，则使用TB作为默认值
            $companyCode = $request->input('company_code');
            if (empty($companyCode)) {
                // 抛出异常
                throw new \Exception('Company code is required.');
            } else {
                // 校验公司编码是否在授权公司列表中
                $user = Auth::user();
                if (!$user->companies->contains('company_code', $companyCode)) {
                    throw new \Exception('Company code is not authorized.');
                }
            }

            $categories = BOM::getProductCategory($companyCode);
            return response()->json($categories);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }

    /**
     * 获取指定BOM信息（包含树结构）
     *
     * @param string $code BOM编码
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function getBOMByCode($code, Request $request)
    {
        try {
            $companyCode = $request->input('company_code', 'TB');

            // 获取展示模式参数，默认为多阶(multi)
            $mode = $request->input('mode', 'multi');

            // 获取有效日期，默认today
            $date = $request->input('date', Carbon::today()->toDateString());
            $date = Carbon::parse($date)->addDays(1)->subSecond();
            
            // 获取可选件参数，默认不包含
            $includeOptional = $request->input('include_optional', false);
            $includeOptional = filter_var($includeOptional, FILTER_VALIDATE_BOOLEAN);
            
            // 获取客户编号参数，默认为null
            $customerCode = $request->input('customer_code');
            
            // 验证模式参数
            if (!in_array($mode, ['multi', 'leaf'])) {
                $mode = 'multi';
            }
            
            // 使用模型方法获取BOM树结构
            $bomTree = BOM::getBOMTreeByCode($companyCode, $code, $mode, $date, $includeOptional, $customerCode);
            
            if (empty($bomTree)) {
                return response()->json([
                    'status' => 'error',
                    'message' => '未找到该BOM记录'
                ], 404);
            }
            
            return response()->json([
                'status' => 'success',
                'data' => $bomTree,
                'mode' => $mode,
                'include_optional' => $includeOptional,
                'customer_code' => $customerCode
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }

    /**
     * 获取客户列表
     * 
     * @param string|null $code BOM料号
     * @return \Illuminate\Http\Response
     */
    public function getCustomers(Request $request)
    {
        try {
            // 获取料号参数
            $code = $request->input('code');
            $companyCode = $request->input('company_code', 'TB');
            
            // 获取有效日期，默认today
            $date = $request->input('date', Carbon::today()->toDateString());
            $date = Carbon::parse($date)->addDays(1)->subSecond();

            // 校验参数是否为空或包含非法字符
            if (empty($code) || !preg_match('/^[a-zA-Z0-9_-]+$/', $code)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid BOM code format.'
                ], 400);
            }
            
            $customers = BOM::getCustomers($companyCode, $code, $date);
                
            return response()->json([
                'status' => 'success',
                'data' => $customers
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }
}
