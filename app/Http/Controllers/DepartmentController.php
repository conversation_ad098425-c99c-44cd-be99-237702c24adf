<?php

namespace App\Http\Controllers;

use App\Models\Department;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DepartmentController extends Controller
{
    /**
     * 显示部门列表
     */
    public function index(Request $request)
    {
        try {
            $search = $request->input('search');
            $status = $request->input('status');
            $departments = Department::getDepartmentList($search, $status);

            return Inertia::render('Settings/Departments/Index', [
                'departments' => $departments,
                'filters' => $request->only(['search', 'status']),
            ]);
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * 显示创建部门表单
     */
    public function create()
    {
        $departments = Department::where('status', '正常')->get();
        // 对于新建部门，获取所有在职员工作为潜在主管
        $managers = \App\Models\User::where('status', '在职')->get();

        return Inertia::render('Settings/Departments/Create', [
            'departments' => $departments,
            'managers' => $managers,
        ]);
    }

    /**
     * 存储新部门
     */
    public function store(Request $request)
    {
        // 参数合法性验证
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'parent_id' => 'nullable|exists:departments,id',
            'manager_ids' => 'nullable|array',
            'manager_ids.*' => 'exists:users,id',
            'description' => 'nullable|string',
            'status' => 'required|in:正常,停用',
            'order' => 'nullable|integer',
        ]);

        try {
            // 分离主管ID数组
            $managerIds = $validated['manager_ids'] ?? [];
            unset($validated['manager_ids']);

            // 调用模型层方法创建部门
            Department::createDepartmentWithManagers($validated, $managerIds);

            return redirect()->route('settings.departments')
                ->with('message', '部门创建成功');
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * 显示部门详情
     */
    public function show(Department $department)
    {
        try {
            $result = Department::getDepartmentWithEmployees($department->id);
            
            return Inertia::render('Settings/Departments/Show', [
                'department' => $result['department'],
                'users' => $result['users']
            ]);
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * 显示编辑部门表单
     */
    public function edit(Department $department)
    {
        $department->load(['parent', 'managers']);
        $departments = Department::where('id', '!=', $department->id)
            ->where('status', '正常')
            ->get();
        $managers = \App\Models\User::where('status', '在职')->get();
        $managerIds = $department->managers->pluck('id')->toArray();

        return Inertia::render('Settings/Departments/Edit', [
            'department' => $department,
            'departments' => $departments,
            'managers' => $managers,
            'managerIds' => $managerIds,
        ]);
    }

    /**
     * 更新部门
     */
    public function update(Request $request, Department $department)
    {
        // 参数合法性验证
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'parent_id' => 'nullable|exists:departments,id',
            'manager_ids' => 'nullable|array',
            'manager_ids.*' => 'exists:users,id',
            'description' => 'nullable|string',
            'status' => 'required|in:正常,停用',
            'order' => 'nullable|integer',
        ]);

        try {
            // 分离主管ID数组
            $managerIds = $validated['manager_ids'] ?? [];
            unset($validated['manager_ids']);

            // 调用模型层方法更新部门
            Department::updateDepartmentWithManagers($department->id, $validated, $managerIds);

            return redirect()->route('settings.departments')
                ->with('message', '部门更新成功');
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * 删除部门
     */
    public function destroy(Department $department)
    {
        try {
            // 调用模型层方法删除部门
            Department::deleteDepartment($department->id);

            return redirect()->route('settings.departments')
                ->with('message', '部门删除成功');
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }
}
