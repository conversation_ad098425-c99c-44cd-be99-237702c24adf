<?php

namespace App\Http\Controllers;

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class PermissionController extends Controller
{
    /**
     * 显示权限列表
     */
    public function index(Request $request)
    {
        try {
            $search = $request->input('search');
            $permissions = Permission::getPermissionList($search);

            return Inertia::render('Settings/Permissions/Index', [
                'permissions' => $permissions,
                'filters' => $request->only(['search']),
            ]);
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * 显示创建权限表单
     */
    public function create()
    {
        $roles = Role::select('id', 'name')->get();
        
        return Inertia::render('Settings/Permissions/Create', [
            'roles' => $roles,
        ]);
    }

    /**
     * 保存新创建的权限
     */
    public function store(Request $request)
    {
        // 参数合法性验证
        $validated = $request->validate([
            'name' => 'required|string|max:50|unique:permissions',
            'description' => 'nullable|string',
            'roles' => 'nullable|array',
            'roles.*' => 'exists:roles,id',
        ]);

        try {
            // 调用模型层方法创建权限
            Permission::createPermissionWithRoles(
                [
                    'name' => $validated['name'],
                    'description' => $validated['description'] ?? null,
                ],
                $validated['roles'] ?? []
            );

            return redirect()->route('settings.permissions')
                ->with('message', '权限创建成功');
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * 显示指定权限
     */
    public function show(Permission $permission)
    {
        try {
            $permission = Permission::getPermissionWithRoles($permission->id);
            
            return Inertia::render('Settings/Permissions/Show', [
                'permission' => $permission,
            ]);
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * 显示编辑权限表单
     */
    public function edit(Permission $permission)
    {
        $permission->load('roles');
        $roles = Role::select('id', 'name')->get();
        
        return Inertia::render('Settings/Permissions/Edit', [
            'permission' => $permission,
            'roles' => $roles,
            'selectedRoles' => $permission->roles->pluck('id'),
        ]);
    }

    /**
     * 更新指定权限
     */
    public function update(Request $request, Permission $permission)
    {
        // 参数合法性验证
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:50', Rule::unique('permissions')->ignore($permission->id)],
            'description' => 'nullable|string',
            'roles' => 'nullable|array',
            'roles.*' => 'exists:roles,id',
        ]);

        try {
            // 调用模型层方法更新权限
            Permission::updatePermissionWithRoles(
                $permission->id,
                [
                    'name' => $validated['name'],
                    'description' => $validated['description'] ?? null,
                ],
                $validated['roles'] ?? []
            );

            return redirect()->route('settings.permissions')
                ->with('message', '权限更新成功');
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * 删除指定权限
     */
    public function destroy(Permission $permission)
    {
        try {
            // 调用模型层方法删除权限
            Permission::deletePermission($permission->id);

            return redirect()->route('settings.permissions')
                ->with('message', '权限已删除');
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }
} 