<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\OrderDetail;
use App\Models\Customer;
use App\Models\Material;
use App\Models\Department;
use App\Models\User;
use App\Models\Currency;
use App\Models\ExchangeRateBasis;
use App\Models\InvoiceType;
use App\Models\ReceiptPaymentTerm;
use App\Models\SalesPricingMethod;
use App\Models\SalesType;
use App\Models\TaxType;
use App\Models\TradeTerm;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class OrderController extends Controller
{
    /**
     * 获取订单列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // 获取分页参数
        $page = (int)$request->input('page', 1);
        $perPage = (int)$request->input('per_page', 10);
        
        // 获取排序参数
        $sortField = $request->input('sort_field');
        $sortDirection = $request->input('sort_direction', 'asc');

        $user = Auth::user();
        // 获取公司筛选参数
        $companyCode = $request->input('company_code');
        if (empty($companyCode)) {
            // 如果公司编码为空，则使用当前用户默认公司编码
            $companyCode = $user->default_company_code;
        } else {
            // 校验公司编码是否在授权公司列表中
            if (!$user->companies->contains('company_code', $companyCode)) {
                throw new \Exception('Company code is not authorized.');
            }
        }

        // 获取搜索参数
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $departmentId = $request->input('department_id', []);
        $salesAccount = $request->input('sales_account', []);
        $customerCode = $request->input('customer_code', []);
        $status = $request->input('status');    
        
        // 验证排序方向
        if (!in_array(strtolower($sortDirection), ['asc', 'desc'])) {
            $sortDirection = 'asc';
        }
        
        // 限制每页数量在10-100之间
        $perPage = max(10, min(100, $perPage));

        try {
            $orders = Order::getOrderList(
                $companyCode, 
                $startDate, 
                $endDate, 
                $departmentId, 
                $salesAccount,
                $customerCode,
                $status,
                $page, 
                $perPage, 
                $sortField, 
                $sortDirection
            );

            return response()->json([
                'status' => 'success',
                'data' => $orders->items(),
                'pagination' => [
                    'total' => $orders->total(),
                    'per_page' => $orders->perPage(),
                    'current_page' => $orders->currentPage(),
                    'last_page' => $orders->lastPage(),
                    'from' => $orders->firstItem() ?? 0,
                    'to' => $orders->lastItem() ?? 0
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取订单表单所需的下拉选项数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFormData(Request $request)
    {
        $companyCode = $request->input('company_code', Auth::user()->default_company_code);
        
        // 获取客户列表
        $customers = Customer::where('company_code', $companyCode)
            ->where('status', 'Y')
            ->select('code as customer_code', 'short_name as customer_name')
            ->orderBy('code')
            ->get();
            
        // 获取销售员列表 - 修复错误，改为数组形式
        $salesPersons = [Auth::user()->only(['account', 'name'])];
            
        // 获取相关码表数据
        $currencies = Currency::select('code as currency_code', 'name as currency_name')->get();
        $departments = Department::where('id', Auth::user()->department_id)->select('id', 'name')->get();
        $exchangeRateBases = ExchangeRateBasis::where('type', 'S')->select('id as exchange_rate_base_code', 'name as exchange_rate_base_name')->get();
        $invoiceTypes = InvoiceType::where('type', '2')->select('code as invoice_type_code', 'name as invoice_type_name')->get();
        $paymentTerms = ReceiptPaymentTerm::where('type', 'S')->select('code as receipt_payment_term_code', 'name as receipt_payment_term_name')->get();
        $pricingMethods = SalesPricingMethod::select('code as sales_pricing_method_code', 'name as sales_pricing_method_name')->get();
        $salesTypes = SalesType::select('code as sales_type_code', 'name as sales_type_name')->get();
        $taxTypes = TaxType::select('code as tax_type_code', 'name as tax_type_name', 'rate as tax_rate','category as tax_category')->get();
        $tradeTerms = TradeTerm::select('code as trade_term_code', 'name as trade_term_name')->get();
        
        return response()->json([
            'currencies' => $currencies,
            'customers' => $customers,
            'departments' => $departments,
            'exchangeRateBases' => $exchangeRateBases,
            'invoiceTypes' => $invoiceTypes,
            'paymentTerms' => $paymentTerms,
            'pricingMethods' => $pricingMethods,
            'salesPersons' => $salesPersons,
            'salesTypes' => $salesTypes,
            'taxTypes' => $taxTypes,
            'tradeTerms' => $tradeTerms,
        ]);
    }
    
    /**
     * 获取物料列表（用于订单明细选择）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMaterials(Request $request)
    {
        $search = $request->input('search', '');
        $companyCode = $request->input('company_code', Auth::user()->default_company_code);
        $page = $request->input('page', 1);
        $perPage = $request->input('perPage', 20);
        
        $query = Material::where('materials.company_code', $companyCode)
            ->where('materials.status', 'Y')
            ->join('material_translations', function ($join) use ($companyCode) {
                $join->on('material_translations.material_code', '=', 'materials.material_code')
                    ->where('material_translations.company_code', '=', $companyCode)
                    ->where('material_translations.locale', '=', Auth::user()->locale);
            });
            
        if ($search) {
            $query->where('materials.figure', 'like', "%$search%")
                ->orWhere('materials.material_code', 'like', "%$search%")
                ->orWhere('material_translations.product_name', 'like', "%$search%")
                ->orWhere('material_translations.specification', 'like', "%$search%");
        } else {
            $query->where('materials.material_code', 'like', '1%');
        }
        
        $total = $query->count();
        $materials = $query->orderBy('materials.material_code')
            ->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get(['materials.material_code','materials.unit', 'material_translations.product_name', 'material_translations.specification']);
            
        return response()->json([
            'materials' => $materials,
            'total' => $total
        ]);
    }

    /**
     * 创建新订单
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // 参数合法性验证
        $validator = Validator::make($request->all(), [
            'company_code' => 'required|string|max:10',
            'customer_code' => 'required|string|max:20',
            'sales_account' => 'required|string|max:20',
            'department_id' => 'required|integer',
            'order_date' => 'required|date',
            'sales_type_code' => 'required|string|max:20',
            'currency_code' => 'required|string|max:20',
            'exchange_rate' => 'required|numeric',
            'exchange_rate_base_code' => 'required|integer',
            'tax_type_code' => 'required|string|max:20',
            'trade_term_code' => 'required|string|max:20',
            'receipt_payment_term_code' => 'required|string|max:20',
            'invoice_type_code' => 'required|string|max:20',
            'pricing_method_code' => 'required|string|max:20',
            'customer_order_number' => 'nullable|string|max:50',
            'tax_rate' => 'nullable|numeric|min:0|max:100',
            'tax_category' => 'nullable|string|in:A,B,C',
            'remark' => 'nullable|string',
            'details' => 'required|array|min:1',
            'details.*.material_code' => 'required|string|max:20',
            'details.*.quantity' => 'required|numeric|min:0.01',
            'details.*.unit_price' => 'required|numeric|min:0',
            'details.*.production_quantity' => 'nullable|numeric|min:0',
            'details.*.tax_rate' => 'nullable|numeric|min:0|max:100',
            'details.*.expected_completion_date' => 'nullable|date',
            'details.*.expected_delivery_date' => 'nullable|date',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // 准备订单数据
            $orderData = $request->only([
                'company_code', 'customer_code', 'sales_account', 'department_id',
                'order_date', 'sales_type_code', 'currency_code', 'exchange_rate',
                'exchange_rate_base_code', 'tax_type_code', 'tax_rate', 'trade_term_code',
                'receipt_payment_term_code', 'invoice_type_code', 'pricing_method_code',
                'customer_order_number', 'tax_category', 'remark'
            ]);

            $detailsData = $request->input('details');
            $userAccount = Auth::user()->account;

            // 调用模型层方法创建订单
            $result = Order::createOrderWithDetails($orderData, $detailsData, $userAccount);
            
            return response()->json([
                'success' => true,
                'message' => '订单创建成功',
                'order' => [
                    'order_code' => $result['order']->order_code,
                    'details' => $result['details']
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '订单创建失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取订单详情
     *
     * @param string $orderCode
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($orderCode)
    {
        try {
            $order = Order::with([
                'customer', 
                'salesPerson', 
                'department',
                'details.material'
            ])->where('order_code', $orderCode)->first();
            
            if (!$order) {
                return response()->json(['error' => '订单不存在'], 404);
            }
            
            return response()->json([
                'order' => $order
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'error' => '获取订单详情失败',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新订单信息
     *
     * @param Request $request
     * @param string $orderCode
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $orderCode)
    {
        // 参数合法性验证
        $validator = Validator::make($request->all(), [
            'customer_code' => 'required|string|max:20',
            'sales_account' => 'required|string|max:20',
            'department_id' => 'required|integer',
            'order_date' => 'required|date',
            'sales_type_code' => 'required|string|max:20',
            'currency_code' => 'required|string|max:20',
            'exchange_rate' => 'required|numeric',
            'exchange_rate_base_code' => 'required|integer',
            'tax_type_code' => 'required|string|max:20',
            'trade_term_code' => 'required|string|max:20',
            'receipt_payment_term_code' => 'required|string|max:20',
            'invoice_type_code' => 'required|string|max:20',
            'pricing_method_code' => 'required|string|max:20',
            'customer_order_number' => 'nullable|string|max:50',
            'tax_rate' => 'nullable|numeric|min:0|max:100',
            'tax_category' => 'nullable|string|in:A,B,C',
            'remark' => 'nullable|string',
            'details' => 'required|array|min:1',
            'details.*.material_code' => 'required|string|max:20',
            'details.*.quantity' => 'required|numeric|min:0.01',
            'details.*.unit_price' => 'required|numeric|min:0',
            'details.*.production_quantity' => 'nullable|numeric|min:0',
            'details.*.tax_rate' => 'nullable|numeric|min:0|max:100',
            'details.*.expected_completion_date' => 'nullable|date',
            'details.*.expected_delivery_date' => 'nullable|date',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // 准备订单数据
            $orderData = $request->only([
                'customer_code', 'sales_account', 'department_id', 'order_date',
                'sales_type_code', 'currency_code', 'exchange_rate', 'exchange_rate_base_code',
                'tax_type_code', 'tax_rate', 'trade_term_code', 'receipt_payment_term_code',
                'invoice_type_code', 'pricing_method_code', 'customer_order_number',
                'tax_category', 'remark'
            ]);

            $detailsData = $request->input('details');
            $userAccount = Auth::user()->account;

            // 调用模型层方法更新订单
            $result = Order::updateOrderWithDetails($orderCode, $orderData, $detailsData, $userAccount);
            
            return response()->json([
                'success' => true,
                'message' => '订单更新成功',
                'order' => [
                    'order_code' => $orderCode,
                    'details' => $result['details']
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '订单更新失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除订单
     *
     * @param string $orderCode
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($orderCode)
    {
        try {
            // 调用模型层方法删除订单
            Order::deleteOrder($orderCode);
            
            return response()->json([
                'success' => true,
                'message' => '订单删除成功'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '订单删除失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 订单审核
     *
     * @param string $orderCode
     * @return \Illuminate\Http\JsonResponse
     */
    public function approve($orderCode)
    {
        try {
            // 调用模型层方法审核订单
            Order::approveOrder($orderCode, Auth::user()->account);
            
            return response()->json([
                'success' => true,
                'message' => '订单审核成功'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '订单审核失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 订单作废
     *
     * @param string $orderCode
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancel($orderCode)
    {
        try {
            // 调用模型层方法作废订单
            Order::cancelOrder($orderCode, Auth::user()->account);
            
            return response()->json([
                'success' => true,
                'message' => '订单已作废'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '订单作废失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 订单结案
     *
     * @param string $orderCode
     * @return \Illuminate\Http\JsonResponse
     */
    public function close($orderCode)
    {
        try {
            // 调用模型层方法结案订单
            Order::closeOrder($orderCode, Auth::user()->account);
            
            return response()->json([
                'success' => true,
                'message' => '订单已结案'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '订单结案失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getCustomerList()
    {
        $customers = Customer::all();
        return response()->json($customers);
    }

    /**
     * 根据客户编号获取客户默认信息
     *
     * @param string $customerCode
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCustomerDefaults($customerCode)
    {
        try {
            // 调用模型层方法获取客户默认信息
            $defaults = Customer::getCustomerDefaults($customerCode);

            return response()->json([
                'success' => true,
                'data' => $defaults
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取客户默认信息失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
