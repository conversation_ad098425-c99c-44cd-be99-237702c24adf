<?php

namespace App\Http\Controllers;

use App\Models\Material;
use Auth;
use Illuminate\Http\Request;

class MaterialController extends Controller
{
    /**
     * 获取产品分类
     * 
     * @param Request $request 请求对象
     * @return \Illuminate\Http\Response
     */
    public function getProductCategory(Request $request)
    {
        try {
            // 从请求参数获取公司编码，如果没有提供，则使用TB作为默认值
            $companyCode = $request->input('company_code');
            if (empty($companyCode)) {
                // 抛出异常
                throw new \Exception('Company code is required.');
            } else {
                // 校验公司编码是否在授权公司列表中
                $user = Auth::user();
                if (!$user->companies->contains('company_code', $companyCode)) {
                    throw new \Exception('Company code is not authorized.');
                }
            }

            $categories = Material::getProductCategory($companyCode);
            return response()->json($categories);
        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取料件列表
     *
     * @param Request $request 请求对象
     * @return \Illuminate\Http\Response
     */
    public function getMaterialList(Request $request)
    {
        // 获取查询参数
        $search = $request->input('search');
        $page = (int)$request->input('page', 1);
        $perPage = (int)$request->input('per_page', 10);

        // 获取排序参数
        $sortField = $request->input('sort_field');
        $sortDirection = $request->input('sort_direction', 'asc');
        
        // 获取产品分类筛选参数
        $categories = $request->input('categories');
        if ($categories && is_string($categories)) {
            $categories = explode(',', $categories);
        }
        
        // 获取公司筛选参数
        $companyCode = $request->input('company_code');
        if (empty($companyCode)) {
            // 抛出异常
            throw new \Exception('Company code is required.');
        } else {
            // 校验公司编码是否在授权公司列表中
            $user = Auth::user();
            if (!$user->companies->contains('company_code', $companyCode)) {
                throw new \Exception('Company code is not authorized.');
            }
        }
        
        // 验证排序方向
        if (!in_array(strtolower($sortDirection), ['asc', 'desc'])) {
            $sortDirection = 'asc';
        }
        
        // 限制每页数量在10-100之间
        $perPage = max(10, min(100, $perPage));
        
        try {
            // 根据搜索条件查询料件信息
            $materials = Material::getMaterialList($companyCode, $page, $perPage, $search, $sortField, $sortDirection, $categories);

            return response()->json([
                'status' => 'success',
                'data' => $materials->items(),
                'pagination' => [
                    'total' => $materials->total(),
                    'per_page' => $materials->perPage(),
                    'current_page' => $materials->currentPage(),
                    'last_page' => $materials->lastPage(),
                    'from' => $materials->firstItem() ?? 0,
                    'to' => $materials->lastItem() ?? 0,
                ],
                'sort' => [
                    'field' => $sortField,
                    'direction' => $sortDirection
                ],
                'categories' => $categories,
                'company_code' => $companyCode
            ]);
        } catch (\Exception $e) {
            // 异常处理
            return response()->json([
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
