<?php

namespace App\Http\Controllers;

use App\Models\Role;
use App\Models\Permission;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class RoleController extends Controller
{
    /**
     * 显示角色列表
     */
    public function index(Request $request)
    {
        try {
            $search = $request->input('search');
            $roles = Role::getRoleList($search);

            return Inertia::render('Settings/Roles/Index', [
                'roles' => $roles,
                'filters' => $request->only(['search']),
            ]);
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * 显示创建角色表单
     */
    public function create()
    {
        $permissions = Permission::select('id', 'name', 'description')->get();
        
        return Inertia::render('Settings/Roles/Create', [
            'permissions' => $permissions,
        ]);
    }

    /**
     * 保存新创建的角色
     */
    public function store(Request $request)
    {
        // 参数合法性验证
        $validated = $request->validate([
            'name' => 'required|string|max:50|unique:roles',
            'description' => 'nullable|string',
            'permissions' => 'nullable|array',
            'permissions.*' => 'exists:permissions,id',
        ]);

        try {
            // 调用模型层方法创建角色
            Role::createRoleWithPermissions(
                [
                    'name' => $validated['name'],
                    'description' => $validated['description'] ?? null,
                ],
                $validated['permissions'] ?? []
            );

            return redirect()->route('settings.roles')
                ->with('message', '角色创建成功');
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * 显示指定角色
     */
    public function show(Role $role)
    {
        try {
            $result = Role::getRoleWithUsers($role->id);
            
            return Inertia::render('Settings/Roles/Show', [
                'role' => $result['role'],
                'users' => $result['users'],
            ]);
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * 显示编辑角色表单
     */
    public function edit(Role $role)
    {
        // 禁止编辑管理员角色
        if ($role->name === 'admin') {
            return redirect()->route('settings.roles')
                ->with('error', '管理员角色不可编辑');
        }
        
        $role->load('permissions');
        $permissions = Permission::select('id', 'name', 'description')->get();
        
        return Inertia::render('Settings/Roles/Edit', [
            'role' => $role,
            'permissions' => $permissions,
            'selectedPermissions' => $role->permissions->pluck('id'),
        ]);
    }

    /**
     * 更新指定角色
     */
    public function update(Request $request, Role $role)
    {
        // 参数合法性验证
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:50', Rule::unique('roles')->ignore($role->id)],
            'description' => 'nullable|string',
            'permissions' => 'nullable|array',
            'permissions.*' => 'exists:permissions,id',
        ]);

        try {
            // 调用模型层方法更新角色
            Role::updateRoleWithPermissions(
                $role->id,
                [
                    'name' => $validated['name'],
                    'description' => $validated['description'] ?? null,
                ],
                $validated['permissions'] ?? []
            );

            return redirect()->route('settings.roles')
                ->with('message', '角色更新成功');
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * 删除指定角色
     */
    public function destroy(Role $role)
    {
        try {
            // 调用模型层方法删除角色
            Role::deleteRole($role->id);

            return redirect()->route('settings.roles')
                ->with('message', '角色删除成功');
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * 为角色分配用户
     */
    public function assignUsers(Request $request, Role $role)
    {
        // 参数合法性验证
        $validated = $request->validate([
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
        ]);

        try {
            // 调用模型层方法分配用户
            Role::assignUsers($role->id, $validated['user_ids']);

            return redirect()->route('settings.roles.show', $role)
                ->with('message', '用户分配成功');
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }
} 