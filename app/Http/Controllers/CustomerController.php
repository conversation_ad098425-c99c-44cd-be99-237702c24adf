<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\Currency;
use App\Models\TaxType;
use App\Models\TradeTerm;
use App\Models\SalesPricingMethod;
use App\Models\InvoiceType;
use App\Models\ReceiptPaymentTerm;
use App\Models\AccountReceivablePayableType;
use App\Models\SalesType;
use App\Models\ExchangeRateBasis;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class CustomerController extends Controller
{
    /**
     * 获取客户列表
     */
    public function index(Request $request)
    {
        // 获取分页参数
        $page = (int)$request->input('page', 1);
        $perPage = (int)$request->input('per_page', 10);
        $search = $request->input('search');
        
        // 获取排序参数
        $sortField = $request->input('sort_field');
        $sortDirection = $request->input('sort_direction', 'asc');
        
        // 获取状态筛选参数
        $status = $request->input('status');
        
        $user = Auth::user();
        // 获取公司筛选参数
        $companyCode = $request->input('company_code');
        if (empty($companyCode)) {
            // 如果公司编码为空，则使用当前用户默认公司编码
            $companyCode = $user->default_company_code;
        } else {
            // 校验公司编码是否在授权公司列表中
            if (!$user->companies->contains('company_code', $companyCode)) {
                throw new \Exception('Company code is not authorized.');
            }
        }
        
        // 验证排序方向
        if (!in_array(strtolower($sortDirection), ['asc', 'desc'])) {
            $sortDirection = 'asc';
        }
        
        // 限制每页数量在10-100之间
        $perPage = max(10, min(100, $perPage));

        try {
            $customers = Customer::getCustomerList($companyCode, $page, $perPage, $search, $sortField, $sortDirection, $status);

            return response()->json([
                'status' => 'success',
                'data' => $customers->items(),
                'pagination' => [
                    'total' => $customers->total(),
                    'per_page' => $customers->perPage(),
                    'current_page' => $customers->currentPage(),
                    'last_page' => $customers->lastPage(),
                    'from' => $customers->firstItem() ?? 0,
                    'to' => $customers->lastItem() ?? 0
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取表单数据
     */
    public function getFormData()
    {
        try {
            // 获取表单需要的下拉数据，只获取有效状态的记录
            $accountReceivableTypes = AccountReceivablePayableType::where('status', 'Y')
                ->where('type', 'R')
                ->select('id', 'code', 'name')
                ->orderBy('code')
                ->get();
                
            $currencies = Currency::where('status', 'Y')
                ->select('id', 'code', 'name', 'symbol')
                ->orderBy('code')
                ->get();
                
            $exchangeRateBases = ExchangeRateBasis::where('status', 'Y')
                ->where('type', 'S')
                ->select('id', 'code', 'name')
                ->orderBy('code')
                ->get();
                
            $invoiceTypes = InvoiceType::where('status', 'Y')
                ->where('type', '2') // 销售发票类型
                ->select('id', 'code', 'name', 'copies')
                ->orderBy('code')
                ->get();
                
            $paymentTerms = ReceiptPaymentTerm::where('status', 'Y')
                ->where('type', 'S') // 销售收款条件
                ->select('id', 'code', 'name')
                ->orderBy('code')
                ->get();
                
            $pricingMethods = SalesPricingMethod::where('status', 'Y')
                ->select('id', 'code', 'name', 'allow_modify', 'allow_zero_price')
                ->orderBy('code')
                ->get();
                
            $salesTypes = SalesType::where('status', 'Y')
                ->select('id', 'code', 'name')
                ->orderBy('code')
                ->get();
                
            $taxTypes = TaxType::where('status', 'Y')
                ->select('id', 'code', 'name', 'rate', 'category')
                ->orderBy('code')
                ->get();
                
            $tradeTerms = TradeTerm::where('status', 'Y')
                ->select('id', 'code', 'name')
                ->orderBy('code')
                ->get();

            return response()->json([
                'status' => 'success',
                'data' => [
                    'accountReceivableTypes' => $accountReceivableTypes,
                    'currencies' => $currencies,
                    'exchangeRateBases' => $exchangeRateBases,
                    'invoiceTypes' => $invoiceTypes,
                    'paymentTerms' => $paymentTerms,
                    'pricingMethods' => $pricingMethods,
                    'salesTypes' => $salesTypes,
                    'taxTypes' => $taxTypes,
                    'tradeTerms' => $tradeTerms,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => '获取表单数据失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 创建客户
     */
    public function store(Request $request)
    {
        // 参数合法性验证
        $validator = Validator::make($request->all(), [
            'company_code' => 'required|string|max:10',
            'short_name' => 'required|string|max:100',
            'full_name' => 'required|string|max:200',
            'currency_id' => 'nullable|exists:currencies,id',
            'tax_type_id' => 'nullable|exists:tax_types,id',
            'trade_term_id' => 'nullable|exists:trade_terms,id',
            'pricing_method_id' => 'nullable|exists:pricing_methods,id',
            'invoice_type_id' => 'nullable|exists:invoice_types,id',
            'payment_term_id' => 'nullable|exists:payment_terms,id',
            'account_receivable_type_id' => 'nullable|exists:account_receivable_types,id',
            'sales_type_id' => 'nullable|exists:sales_types,id',
            'exchange_rate_basis_id' => 'nullable|exists:exchange_rate_bases,id',
            'tax_number' => 'nullable|string|max:50',
            'status' => ['required', Rule::in(['Y', 'N'])],
            'remarks' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // 调用模型层方法创建客户
            $customer = Customer::createCustomer($request->all());

            return response()->json([
                'status' => 'success',
                'message' => '客户创建成功',
                'data' => $customer
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => '客户创建失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取客户详情
     */
    public function show(string $id)
    {
        try {
            $customer = Customer::getCustomerWithRelations($id);

            return response()->json([
                'status' => 'success',
                'data' => $customer
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => '获取客户详情失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新客户信息
     */
    public function update(Request $request, string $id)
    {
        // 参数合法性验证
        $validator = Validator::make($request->all(), [
            'short_name' => 'required|string|max:100',
            'full_name' => 'required|string|max:200',
            'currency_id' => 'nullable|exists:currencies,id',
            'tax_type_id' => 'nullable|exists:tax_types,id',
            'trade_term_id' => 'nullable|exists:trade_terms,id',
            'pricing_method_id' => 'nullable|exists:pricing_methods,id',
            'invoice_type_id' => 'nullable|exists:invoice_types,id',
            'payment_term_id' => 'nullable|exists:payment_terms,id',
            'account_receivable_type_id' => 'nullable|exists:account_receivable_types,id',
            'sales_type_id' => 'nullable|exists:sales_types,id',
            'exchange_rate_basis_id' => 'nullable|exists:exchange_rate_bases,id',
            'tax_number' => 'nullable|string|max:50',
            'status' => ['required', Rule::in(['Y', 'N'])],
            'remarks' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // 调用模型层方法更新客户
            $customer = Customer::updateCustomer($id, $request->all());

            return response()->json([
                'status' => 'success',
                'message' => '客户更新成功',
                'data' => $customer
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => '客户更新失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除客户（软删除）
     */
    public function destroy(string $id)
    {
        try {
            // 调用模型层方法删除客户
            Customer::deleteCustomer($id);

            return response()->json([
                'status' => 'success',
                'message' => '客户删除成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => '客户删除失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取简单客户列表
     */
    public function getSimpleList()
    {
        try {
            $customers = Customer::getSimpleCustomerList();
            return response()->json($customers);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => '获取客户列表失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
