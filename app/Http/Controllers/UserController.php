<?php

namespace App\Http\Controllers;

use App\Models\Company;
use App\Models\Role;
use App\Models\User;
use App\Models\Department;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            $search = $request->input('search');
            $status = $request->input('status');
            $departmentId = $request->input('department_id');
            
            $users = User::getUserList($search, $status, $departmentId);

            return Inertia::render('Settings/Users/<USER>', [
                'users' => $users,
                'filters' => $request->only(['search', 'status', 'department']),
            ]);
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $roles = Role::all();
        // 获取所有部门并按层级排序
        $departments = Department::getDepartmentsWithLevel();
        $companies = Company::where('is_active', 'Y')
                    ->join('company_translations', 'companies.company_code', '=', 'company_translations.company_code')
                    ->where('company_translations.locale', Auth::user()->locale)
                    ->orderBy('company_code')
                    ->get(['companies.company_code', 'company_translations.company_name']);

        return Inertia::render('Settings/Users/<USER>', [
            'roles' => $roles,
            'departments' => $departments,
            'companies' => $companies,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // 参数合法性验证
        $validated = $request->validate([
            'account' => 'required|string|max:255',
            'locale' => 'required|exists:locale,locale',
            'default_company_code' => 'required|exists:companies,company_code',
            'company_codes' => 'required|array',
            'company_codes.*' => 'exists:companies,company_code',
            'name' => 'required|string|max:255',
            'email' => 'nullable|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'employee_id' => 'nullable|string|max:50|unique:users',
            'department_id' => 'nullable|exists:departments,id',
            'position' => 'nullable|string|max:100',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'hire_date' => 'nullable|date',
            'status' => 'required|string|in:在职,离职,休假',
            'emergency_contact' => 'nullable|string|max:50',
            'emergency_phone' => 'nullable|string|max:20',
            'notes' => 'nullable|string',
            'roles' => 'nullable|array',
            'roles.*' => 'exists:roles,id',
        ]);

        try {
            // 准备用户数据
            $userData = $validated;
            $userData['password'] = Hash::make($validated['password']);
            
            $companyIds = $validated['company_codes'];
            $roleIds = $validated['roles'] ?? [];
            
            // 移除不属于用户表的字段
            unset($userData['company_codes'], $userData['roles'], $userData['password_confirmation']);

            // 调用模型层方法创建用户
            User::createUserWithRelations($userData, $companyIds, $roleIds);

            return redirect()->route('settings.users')
                ->with('message', '人员创建成功');
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user)
    {
        try {
            $user = User::getUserWithRelations($user->id);
            $userRoles = $user->roles;

            // 获取所有可用公司列表
            $companies = Company::where('is_active', 'Y')
                        ->join('company_translations', 'companies.company_code', '=', 'company_translations.company_code')
                        ->where('company_translations.locale', Auth::user()->locale)
                        ->orderBy('company_code')
                        ->get(['companies.company_code', 'company_translations.company_name']);

            return Inertia::render('Settings/Users/<USER>', [
                'user' => $user,
                'userRoles' => $userRoles,
                'companies' => $companies,
            ]);
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user)
    {
        $roles = Role::all();
        $userRoles = $user->roles->pluck('id')->toArray();

        // 获取所有部门并按层级排序
        $departments = Department::getDepartmentsWithLevel();

        $companies = Company::where('is_active', 'Y')
                    ->join('company_translations', 'companies.company_code', '=', 'company_translations.company_code')
                    ->where('company_translations.locale', Auth::user()->locale)
                    ->orderBy('company_code')
                    ->get(['companies.company_code', 'company_translations.company_name']);

        $userCompanyCodes = $user->companies->pluck('company_code')->toArray();

        return Inertia::render('Settings/Users/<USER>', [
            'user' => $user,
            'roles' => $roles,
            'userRoles' => $userRoles,
            'departments' => $departments,
            'companies' => $companies,
            'userCompanyCodes' => $userCompanyCodes,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $user)
    {
        // 参数合法性验证
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['nullable', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'password' => 'nullable|string|min:8|confirmed',
            'employee_id' => ['nullable', 'string', 'max:50', Rule::unique('users')->ignore($user->id)],
            'default_company_code' => 'required|exists:companies,company_code',
            'company_codes' => 'required|array',
            'company_codes.*' => 'exists:companies,company_code',
            'department_id' => 'nullable|exists:departments,id',
            'position' => 'nullable|string|max:100',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'hire_date' => 'nullable|date',
            'status' => 'required|string|in:在职,离职,休假',
            'emergency_contact' => 'nullable|string|max:50',
            'emergency_phone' => 'nullable|string|max:20',
            'notes' => 'nullable|string',
            'roles' => 'nullable|array',
            'roles.*' => 'exists:roles,id',
        ]);

        try {
            // 准备用户数据
            $userData = $validated;
            
            // 只有在提供密码时才更新密码
            if (!empty($validated['password'])) {
                $userData['password'] = Hash::make($validated['password']);
            } else {
                unset($userData['password']);
            }
            
            $companyIds = $validated['company_codes'];
            $roleIds = $validated['roles'] ?? [];
            
            // 移除不属于用户表的字段
            unset($userData['company_codes'], $userData['roles'], $userData['password_confirmation']);

            // 调用模型层方法更新用户
            User::updateUserWithRelations($user->id, $userData, $companyIds, $roleIds);

            return redirect()->route('settings.users')
                ->with('message', '人员信息更新成功');
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {
        try {
            // 调用模型层方法删除用户
            User::deleteUser($user->id, Auth::id());

            return redirect()->route('settings.users')
                ->with('message', '人员已删除');
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * 获取用户的公司列表
     */
    public function getUserCompanies()
    {
        try {
            $companies = User::getUserCompanies(Auth::id());
            return response()->json($companies);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * 获取用户列表
     */
    public function getUserList()
    {
        try {
            $users = User::getSimpleUserList();
            return response()->json($users);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}