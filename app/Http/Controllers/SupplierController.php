<?php

namespace App\Http\Controllers;

use App\Models\Supplier;
use App\Models\Currency;
use App\Models\TaxType;
use App\Models\TradeTerm;
use App\Models\PurchasePricingMethod;
use App\Models\InvoiceType;
use App\Models\ReceiptPaymentTerm;
use App\Models\AccountReceivablePayableType;
use App\Models\PurchaseType;
use App\Models\ExchangeRateBasis;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class SupplierController extends Controller
{
    /**
     * 获取供应商列表
     */
    public function index(Request $request)
    {
        // 获取分页参数
        $page = (int)$request->input('page', 1);
        $perPage = (int)$request->input('per_page', 10);
        $search = $request->input('search');
        
        // 获取排序参数
        $sortField = $request->input('sort_field');
        $sortDirection = $request->input('sort_direction', 'asc');
        
        // 获取状态筛选参数
        $status = $request->input('status');
        
        // 验证排序方向
        if (!in_array(strtolower($sortDirection), ['asc', 'desc'])) {
            $sortDirection = 'asc';
        }
        
        // 限制每页数量在10-100之间
        $perPage = max(10, min(100, $perPage));

        try {
            $suppliers = Supplier::getSupplierList($page, $perPage, $search, $sortField, $sortDirection, $status);

            return response()->json([
                'status' => 'success',
                'data' => $suppliers->items(),
                'pagination' => [
                    'total' => $suppliers->total(),
                    'per_page' => $suppliers->perPage(),
                    'current_page' => $suppliers->currentPage(),
                    'last_page' => $suppliers->lastPage(),
                    'from' => $suppliers->firstItem() ?? 0,
                    'to' => $suppliers->lastItem() ?? 0
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取表单数据
     */
    public function getFormData()
    {
        try {
            // 获取表单需要的下拉数据，只获取有效状态的记录
            $currencies = Currency::where('status', 'Y')
                ->select('id', 'code', 'name', 'symbol')
                ->orderBy('code')
                ->get();
                
            $taxTypes = TaxType::where('status', 'Y')
                ->select('id', 'code', 'name', 'rate', 'category')
                ->orderBy('code')
                ->get();
                
            $tradeTerms = TradeTerm::where('status', 'Y')
                ->select('id', 'code', 'name')
                ->orderBy('code')
                ->get();
                
            $pricingMethods = PurchasePricingMethod::where('status', 'Y')
                ->select('id', 'code', 'name', 'allow_modify', 'allow_zero_price')
                ->orderBy('code')
                ->get();
                
            $invoiceTypes = InvoiceType::where('status', 'Y')
                ->where('type', '1') // 采购发票类型
                ->select('id', 'code', 'name', 'copies')
                ->orderBy('code')
                ->get();
                
            $paymentTerms = ReceiptPaymentTerm::where('status', 'Y')
                ->where('type', 'P') // 采购付款条件
                ->select('id', 'code', 'name')
                ->orderBy('code')
                ->get();
                
            $accountPayableTypes = AccountReceivablePayableType::where('status', 'Y')
                ->where('type', 'P')
                ->select('id', 'code', 'name')
                ->orderBy('code')
                ->get();
                
            $purchaseTypes = PurchaseType::where('status', 'Y')
                ->select('id', 'code', 'name')
                ->orderBy('code')
                ->get();
                
            $exchangeRateBases = ExchangeRateBasis::where('status', 'Y')
                ->where('type', 'P')
                ->select('id', 'code', 'name')
                ->orderBy('code')
                ->get();

            return response()->json([
                'status' => 'success',
                'data' => [
                    'currencies' => $currencies,
                    'taxTypes' => $taxTypes,
                    'tradeTerms' => $tradeTerms,
                    'pricingMethods' => $pricingMethods,
                    'invoiceTypes' => $invoiceTypes,
                    'paymentTerms' => $paymentTerms,
                    'accountPayableTypes' => $accountPayableTypes,
                    'purchaseTypes' => $purchaseTypes,
                    'exchangeRateBases' => $exchangeRateBases,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => '获取表单数据失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 创建供应商
     */
    public function store(Request $request)
    {
        // 参数合法性验证
        $validator = Validator::make($request->all(), [
            'short_name' => 'required|string|max:100',
            'full_name' => 'required|string|max:200',
            'currency_id' => 'nullable|exists:currencies,id',
            'tax_type_id' => 'nullable|exists:tax_types,id',
            'trade_term_id' => 'nullable|exists:trade_terms,id',
            'pricing_method_id' => 'nullable|exists:purchase_pricing_methods,id',
            'invoice_type_id' => 'nullable|exists:invoice_types,id',
            'payment_term_id' => 'nullable|exists:receipt_payment_terms,id',
            'account_payable_type_id' => 'nullable|exists:account_receivable_payable_types,id',
            'purchase_type_id' => 'nullable|exists:purchase_types,id',
            'domestic_foreign_type_id' => 'nullable|exists:domestic_foreign_types,id',
            'exchange_rate_basis_id' => 'nullable|exists:exchange_rate_bases,id',
            'tax_number' => 'nullable|string|max:50',
            'status' => ['required', Rule::in(['Y', 'N'])],
            'remarks' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // 调用模型层方法创建供应商
            $supplier = Supplier::createSupplier($request->all());

            return response()->json([
                'status' => 'success',
                'message' => '供应商创建成功',
                'data' => $supplier
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => '供应商创建失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取供应商详情
     */
    public function show(string $id)
    {
        try {
            $supplier = Supplier::getSupplierWithRelations($id);

            return response()->json([
                'status' => 'success',
                'data' => $supplier
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => '获取供应商详情失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新供应商信息
     */
    public function update(Request $request, string $id)
    {
        // 参数合法性验证
        $validator = Validator::make($request->all(), [
            'short_name' => 'required|string|max:100',
            'full_name' => 'required|string|max:200',
            'currency_id' => 'nullable|exists:currencies,id',
            'tax_type_id' => 'nullable|exists:tax_types,id',
            'trade_term_id' => 'nullable|exists:trade_terms,id',
            'pricing_method_id' => 'nullable|exists:purchase_pricing_methods,id',
            'invoice_type_id' => 'nullable|exists:invoice_types,id',
            'payment_term_id' => 'nullable|exists:receipt_payment_terms,id',
            'account_payable_type_id' => 'nullable|exists:account_receivable_payable_types,id',
            'purchase_type_id' => 'nullable|exists:purchase_types,id',
            'domestic_foreign_type_id' => 'nullable|exists:domestic_foreign_types,id',
            'exchange_rate_basis_id' => 'nullable|exists:exchange_rate_bases,id',
            'tax_number' => 'nullable|string|max:50',
            'status' => ['required', Rule::in(['Y', 'N'])],
            'remarks' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // 调用模型层方法更新供应商
            $supplier = Supplier::updateSupplier($id, $request->all());

            return response()->json([
                'status' => 'success',
                'message' => '供应商更新成功',
                'data' => $supplier
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => '供应商更新失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除供应商（软删除）
     */
    public function destroy(string $id)
    {
        try {
            // 调用模型层方法删除供应商
            Supplier::deleteSupplier($id);

            return response()->json([
                'status' => 'success',
                'message' => '供应商删除成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => '供应商删除失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
} 