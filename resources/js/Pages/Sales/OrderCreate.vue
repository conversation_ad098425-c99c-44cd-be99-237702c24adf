<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, onUnmounted } from 'vue';
import { router } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import TextInput from '@/Components/TextInput.vue';
import InputLabel from '@/Components/InputLabel.vue';
import InputError from '@/Components/InputError.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DialogModal from '@/Components/DialogModal.vue';
import apiService from '@/Utils/ApiService';

// 定义props
interface OrderCreateProps {
  company_code?: string;
}

const props = defineProps<OrderCreateProps>();

// 物料接口定义
interface Material {
  material_code: string;
  product_name: string;
  specification: string;
  unit: string;
  base_price?: number;
}

// 定义订单明细项接口
interface OrderDetailItem {
  order_item: number; // 项次，从1开始自增
  status: number; // 行状态，默认1：一般
  material_code: string; // 料号
  material_name: string; // 品名（从material_translations表获取）
  material_spec: string; // 规格（从material_translations表获取）
  customer_material_code: string; // 客户料号
  customer_material_specification: string; // 客户料号规格
  unit: string; // 单位（从materials表获取）
  unit_price: number; // 单价
  standard_unit_price: number; // 标准单价，默认0
  quantity: number; // 订购数量
  production_quantity: number; // 生产数量，默认同步订购数量
  expected_completion_date: string; // 预计完工日期
  expected_delivery_date: string; // 预订交货日期
  tax_type_code: string; // 税种，从订单信息同步
  tax_rate: number; // 税率，从订单信息同步
  total_price_no_tax: number; // 税前金额，自动计算
  total_price_tax: number; // 含税金额，自动计算
  tax_amount: number; // 税额，自动计算
  remark: string; // 备注
  clear_table_number: string; // 清表编号
  brand: string; // 商标
  color: string; // 颜色
  description: string; // 说明书
  warning: string; // 警告
  inner_packing: string; // 内包装
  outer_packing: string; // 外包装
  borrow: string; // 借用
  remark_2: string; // 长备注
}

// 定义订单表单数据接口
interface OrderFormData {
  company_code: string;
  customer_code: string;
  sales_account: string;
  department_id: number;
  order_date: string;
  sales_type_code: string;
  currency_code: string;
  exchange_rate: number;
  exchange_rate_base_code: number;
  tax_type_code: string;
  tax_rate: number;
  tax_category: string;
  trade_term_code: string;
  receipt_payment_term_code: string;
  customer_order_number: string; // 客户订单号
  invoice_type_code: string;
  pricing_method_code: string;
  remark: string;
  details: OrderDetailItem[];
}

// 定义表单选项数据接口
interface FormData {
  companies?: Array<{company_code: string, company_name: string}>;
  customers: Array<{customer_code: string, customer_name: string}>;
  salesPersons: Array<{account: string, name: string}>;
  departments: Array<{id: number, name: string}>;
  salesTypes: Array<{sales_type_code: string, sales_type_name: string}>;
  currencies: Array<{currency_code: string, currency_name: string}>;
  exchangeRateBases: Array<{exchange_rate_base_code: number, exchange_rate_base_name: string}>;
  taxTypes: Array<{tax_type_code: string, tax_type_name: string, tax_rate: number, tax_category: string}>;
  tradeTerms: Array<{trade_term_code: string, trade_term_name: string}>;
  paymentTerms: Array<{receipt_payment_term_code: string, receipt_payment_term_name: string}>;
  invoiceTypes: Array<{invoice_type_code: string, invoice_type_name: string}>;
  pricingMethods: Array<{sales_pricing_method_code: string, sales_pricing_method_name: string}>;
}

// 初始化表单数据
const formData = ref<FormData>({
  customers: [],
  salesPersons: [],
  departments: [],
  salesTypes: [],
  currencies: [],
  exchangeRateBases: [],
  taxTypes: [],
  tradeTerms: [],
  paymentTerms: [],
  invoiceTypes: [],
  pricingMethods: [],
});

// 初始化订单表单
const orderForm = reactive<OrderFormData>({
  company_code: props.company_code || '',
  customer_code: '',
  sales_account: '',
  department_id: 0,
  order_date: new Date().toISOString().split('T')[0],
  sales_type_code: '',
  currency_code: '',
  exchange_rate: 1,
  exchange_rate_base_code: 0,
  tax_type_code: '',
  tax_rate: 13,
  tax_category: 'B',//默认含税 A:零税 B:含税 C:不含税
  trade_term_code: '',
  receipt_payment_term_code: '',
  customer_order_number: '', // 客户订单号
  invoice_type_code: '',
  pricing_method_code: '',
  remark: '',
  details: []
});

// 物料选择相关状态
const materialDialogVisible = ref<boolean>(false);
const materialSearch = ref<string>('');
const materialList = ref<Material[]>([]);
const materialPage = ref<number>(1);
const materialPerPage = ref<number>(15);
const materialTotal = ref<number>(0);
const selectedMaterials = ref<Set<string>>(new Set()); // 新增：已选中的物料编码集合

// 客户选择相关状态
const customerSearch = ref<string>('');
const showCustomerDropdown = ref<boolean>(false);
const filteredCustomers = computed(() => {
  if (!customerSearch.value) {
    return formData.value.customers || [];
  }

  const search = customerSearch.value.toLowerCase();
  return (formData.value.customers || []).filter(
    customer => customer.customer_code.toLowerCase().includes(search) ||
                customer.customer_name.toLowerCase().includes(search)
  );
});

// 处理客户搜索框按键事件
const handleCustomerSearchKeydown = (event: KeyboardEvent): void => {
  // 如果按下回车键且搜索结果只有一个客户
  if (event.key === 'Enter' && filteredCustomers.value.length === 1) {
    event.preventDefault();
    // 自动选中唯一的客户
    orderForm.customer_code = filteredCustomers.value[0].customer_code;
    showCustomerDropdown.value = false;
    handleCustomerChange();
  }
};

// 获取默认的销售员和部门
const loadDefaultValues = (): void => {
  // 如果有销售员数据且未设置销售员，则自动选择第一个
  if (formData.value.salesPersons && formData.value.salesPersons.length > 0 && !orderForm.sales_account) {
    orderForm.sales_account = formData.value.salesPersons[0].account;
  }

  // 如果有部门数据且未设置部门，则自动选择第一个
  if (formData.value.departments && formData.value.departments.length > 0 && (!orderForm.department_id || orderForm.department_id === 0)) {
    orderForm.department_id = Number(formData.value.departments[0].id);
  }
};

// 错误和加载状态
const errors = ref<Record<string, string>>({});
const loading = ref<boolean>(false);

// 表单验证
const validateForm = (): boolean => {
  const newErrors: Record<string, string> = {};

  // 必填字段验证
  if (!orderForm.customer_code) newErrors.customer_code = '请选择客户';
  if (!orderForm.sales_account) newErrors.sales_account = '请选择销售员';
  if (!orderForm.department_id || orderForm.department_id === 0) newErrors.department_id = '请选择部门';
  if (!orderForm.order_date) newErrors.order_date = '请选择订单日期';
  if (!orderForm.sales_type_code) newErrors.sales_type_code = '请选择销售类型';
  if (!orderForm.currency_code) newErrors.currency_code = '请选择币种';
  if (!orderForm.exchange_rate_base_code || orderForm.exchange_rate_base_code === 0) newErrors.exchange_rate_base_code = '请选择汇率基准';
  if (!orderForm.tax_type_code) newErrors.tax_type_code = '请选择税种';
  if (!orderForm.trade_term_code) newErrors.trade_term_code = '请选择交易条件';
  if (!orderForm.receipt_payment_term_code) newErrors.receipt_payment_term_code = '请选择收款条件';
  if (!orderForm.invoice_type_code) newErrors.invoice_type_code = '请选择发票类型';
  if (!orderForm.pricing_method_code) newErrors.pricing_method_code = '请选择取价方式';

  // 验证订单是否有明细
  if (orderForm.details.length === 0) {
    newErrors.details = '请至少添加一项产品';
  }

  errors.value = newErrors;
  return Object.keys(newErrors).length === 0;
};

// 计算属性：订单总数量
const totalItems = computed<number>(() => {
  return orderForm.details.reduce((sum, item) => sum + (parseFloat(String(item.quantity)) || 0), 0);
});

// 计算属性：订单总金额
const orderTotal = computed<number>(() => {
  return calculateOrderTotal();
});

// 加载表单数据
const loadFormData = async (): Promise<void> => {
  try {
    // 传递公司代码参数
    const params: Record<string, any> = {};
    if (orderForm.company_code) {
      params.company_code = orderForm.company_code;
    }

    const response = await apiService.get('sales/orders/form-data', params);
    console.log('表单数据响应:', response.data);
    formData.value = response.data;

    // 如果没有设置公司代码且只有一个公司，默认选择
    if (!orderForm.company_code && formData.value.companies && formData.value.companies.length === 1) {
      orderForm.company_code = formData.value.companies[0].company_code;
    }

    // 确保salesPersons始终为数组
    if (!Array.isArray(formData.value.salesPersons)) {
      console.warn('salesPersons不是数组，进行修正');
      formData.value.salesPersons = formData.value.salesPersons ? [formData.value.salesPersons] : [];
    }

    // 加载默认的销售员和部门
    loadDefaultValues();

  } catch (error: any) {
    console.error('加载表单数据失败', error);
    console.error('错误详情:', error.response?.data || '无响应数据');
    // 设置一些默认空数组，确保UI不会崩溃
    formData.value = {
      customers: [],
      salesPersons: [],
      departments: [],
      salesTypes: [],
      currencies: [],
      exchangeRateBases: [],
      taxTypes: [],
      tradeTerms: [],
      paymentTerms: [],
      invoiceTypes: [],
      pricingMethods: [],
    };
  }
};

// 处理客户变更
const handleCustomerChange = async (): Promise<void> => {
  if (!orderForm.customer_code) return;

  loading.value = true;

  try {
    // 调用API获取客户的默认属性和发票信息
    const response = await apiService.get(`sales/orders/customer-defaults/${orderForm.customer_code}`);

    if (response.data.success) {
      const defaults = response.data.data;

      // 保存旧的币种值，用于检测是否变更
      const oldCurrencyCode = orderForm.currency_code;

      // 填充客户默认值
      orderForm.sales_type_code = defaults.sales_type_code || orderForm.sales_type_code;
      orderForm.currency_code = defaults.currency_code || orderForm.currency_code;
      orderForm.exchange_rate_base_code = defaults.exchange_rate_base_code ? Number(defaults.exchange_rate_base_code) : orderForm.exchange_rate_base_code;
      orderForm.tax_type_code = defaults.tax_type_code || orderForm.tax_type_code;
      orderForm.trade_term_code = defaults.trade_term_code || orderForm.trade_term_code;
      orderForm.receipt_payment_term_code = defaults.receipt_payment_term_code || orderForm.receipt_payment_term_code;
      orderForm.invoice_type_code = defaults.invoice_type_code || orderForm.invoice_type_code;
      orderForm.pricing_method_code = defaults.pricing_method_code || orderForm.pricing_method_code;

      // 更新税率
      updateTaxRateFromTaxType();

      // 如果有税率，则更新默认明细的税率
      if (orderForm.details.length > 0) {
        orderForm.details.forEach((detail, index) => {
          detail.tax_type_code = orderForm.tax_type_code;
          detail.tax_rate = orderForm.tax_rate;
          // 需要重新计算价格
          updateDetailPrice(index);
        });
      }

      // 如果币种发生变化，则更新汇率
      if (oldCurrencyCode !== orderForm.currency_code) {
        await updateExchangeRate();
      }
    }
  } catch (error) {
    console.error('获取客户默认信息失败', error);
  } finally {
    loading.value = false;
  }
};

// 新增：从税种更新税率和税种分类
const updateTaxRateFromTaxType = (): void => {
  if (orderForm.tax_type_code && formData.value.taxTypes) {
    const selectedTaxType = formData.value.taxTypes.find(t => t.tax_type_code === orderForm.tax_type_code);
    if (selectedTaxType) {
      orderForm.tax_rate = selectedTaxType.tax_rate;
      orderForm.tax_category = selectedTaxType.tax_category || 'B'; // 默认含税
    }
  }
};

// 更新汇率
const updateExchangeRate = async (): Promise<void> => {
  // 设置默认汇率
  if (orderForm.currency_code === 'CNY') {
    orderForm.exchange_rate = 1;
    return;
  }

  // 如果选择了美元，则从API获取最新汇率
  if (orderForm.currency_code === 'USD') {
    try {
      await fetchExchangeRate();
    } catch (error) {
      console.error('获取汇率失败:', error);
    }
  }
};

// 从后端API获取美元兑人民币汇率
const fetchExchangeRate = async (): Promise<void> => {
  try {
    loading.value = true;
    // 使用我们自己的API获取美元兑人民币汇率
    const response = await apiService.get('exchange-rates/usd-cny');
    console.log('获取汇率响应:', response.data);
    // 解析响应数据
    if (response.data && response.data.success && response.data.rate) {
      // 设置汇率，保留6位小数
      orderForm.exchange_rate = parseFloat(parseFloat(response.data.rate).toFixed(6));
      console.log('获取到美元兑人民币汇率:', orderForm.exchange_rate);
    } else {
      console.error('获取汇率数据失败:', response.data.message || '未知错误');
      // 设置一个默认汇率，避免出错
      orderForm.exchange_rate = 7.1;
    }
  } catch (error) {
    console.error('获取汇率数据失败:', error);
    // 设置一个默认汇率，避免出错
    orderForm.exchange_rate = 7.1;
  } finally {
    loading.value = false;
  }
};

// 打开物料选择对话框
const openMaterialDialog = (): void => {
  // 检查是否已选择客户
  if (!orderForm.customer_code) {
    alert('请先选择客户后再添加物料');
    return;
  }

  materialDialogVisible.value = true;

  // 初始化已选中的物料状态
  selectedMaterials.value.clear();
  orderForm.details.forEach(detail => {
    selectedMaterials.value.add(detail.material_code);
  });

  searchMaterials();
};

// 关闭物料选择对话框
const closeMaterialDialog = (): void => {
  materialDialogVisible.value = false;
  materialSearch.value = '';
  materialPage.value = 1;
  // 清空选中状态
  selectedMaterials.value.clear();
};

// 搜索物料
const searchMaterials = async (): Promise<void> => {
  try {
    const response = await apiService.get('sales/orders/materials', {
      search: materialSearch.value,
      page: materialPage.value,
      perPage: materialPerPage.value,
      company_code: orderForm.company_code
    });

    materialList.value = response.data.materials;
    materialTotal.value = response.data.total;
  } catch (error) {
    console.error('搜索物料失败', error);
  }
};

// 计算属性：排序后的物料列表（已选中的置顶）
const sortedMaterialList = computed(() => {
  return [...materialList.value].sort((a, b) => {
    const aSelected = selectedMaterials.value.has(a.material_code);
    const bSelected = selectedMaterials.value.has(b.material_code);

    if (aSelected && !bSelected) return -1;
    if (!aSelected && bSelected) return 1;
    return 0;
  });
});

// 物料分页
const nextMaterialPage = (): void => {
  if (materialPage.value < Math.ceil(materialTotal.value / materialPerPage.value)) {
    materialPage.value++;
    searchMaterials();
  }
};

const prevMaterialPage = (): void => {
  if (materialPage.value > 1) {
    materialPage.value--;
    searchMaterials();
  }
};

// 选择物料
const selectMaterial = (material: Material): void => {
  // 检查是否已添加该物料
  const existingIndex = orderForm.details.findIndex(d => d.material_code === material.material_code);

  if (existingIndex >= 0) {
    // 如果已存在，增加数量
    orderForm.details[existingIndex].quantity = orderForm.details[existingIndex].quantity + 1;
    updateDetailPrice(existingIndex);
  } else {
    // 获取该物料的默认价格（这里可以添加API调用获取客户特定价格）
    const defaultPrice = material.base_price || 0;

    // 添加新物料到订单明细
    const newDetail: OrderDetailItem = {
      order_item: orderForm.details.length + 1,
      status: 1,
      material_code: material.material_code,
      material_name: material.product_name,
      material_spec: material.specification,
      customer_material_code: '',
      customer_material_specification: '',
      unit: material.unit,
      unit_price: defaultPrice,
      standard_unit_price: 0,
      quantity: 1,
      production_quantity: 0, // 初始值为0
      expected_completion_date: '',
      expected_delivery_date: '',
      tax_type_code: orderForm.tax_type_code,
      tax_rate: orderForm.tax_rate,
      total_price_no_tax: 0,
      total_price_tax: 0,
      tax_amount: 0,
      remark: '',
      clear_table_number: '',
      brand: '',
      color: '',
      description: '',
      warning: '',
      inner_packing: '',
      outer_packing: '',
      borrow: '',
      remark_2: ''
    };

    orderForm.details.push(newDetail);

    // 计算初始金额
    calculateDetailAmounts(newDetail);
  }

  // 添加到已选中集合
  selectedMaterials.value.add(material.material_code);
};

// 处理税种变更
const handleTaxTypeChange = (): void => {
  // 更新订单的税率和税种分类
  updateTaxRateFromTaxType();

  // 更新所有明细的税种、税率，并重新计算金额
  orderForm.details.forEach((detail, index) => {
    detail.tax_type_code = orderForm.tax_type_code;
    detail.tax_rate = orderForm.tax_rate;
    // 重新计算金额（会根据新的税种分类进行计算）
    calculateDetailAmounts(detail);
  });
};

// 新增：切换物料选中状态
const toggleMaterialSelection = (material: Material): void => {
  if (selectedMaterials.value.has(material.material_code)) {
    // 如果已选中，则取消选中并从订单明细中移除
    selectedMaterials.value.delete(material.material_code);
    const existingIndex = orderForm.details.findIndex(d => d.material_code === material.material_code);
    if (existingIndex >= 0) {
      orderForm.details.splice(existingIndex, 1);
    }
  } else {
    // 如果未选中，则选中并添加到订单明细
    selectMaterial(material);
  }
};

// 新增：检查物料是否已选中
const isMaterialSelected = (materialCode: string): boolean => {
  return selectedMaterials.value.has(materialCode);
};

// 新增：批量添加选中的物料
const addSelectedMaterials = (): void => {
  // 关闭对话框
  closeMaterialDialog();
};

// 新增：获取已选中物料的名称
const getSelectedMaterialName = (materialCode: string): string => {
  // 首先从当前页面的物料列表中查找
  const material = materialList.value.find(m => m.material_code === materialCode);
  if (material) {
    return material.product_name;
  }

  // 如果当前页面没有，从订单明细中查找
  const detail = orderForm.details.find(d => d.material_code === materialCode);
  if (detail) {
    return detail.material_name;
  }

  // 如果都没找到，返回物料编码
  return materialCode;
};

// 新增：移除单个已选中的物料
const removeSelectedMaterial = (materialCode: string): void => {
  selectedMaterials.value.delete(materialCode);

  // 同时从订单明细中移除
  const existingIndex = orderForm.details.findIndex(d => d.material_code === materialCode);
  if (existingIndex >= 0) {
    orderForm.details.splice(existingIndex, 1);
  }
};

// 新增：清空所有已选中的物料
const clearAllSelectedMaterials = (): void => {
  selectedMaterials.value.clear();

  // 清空订单明细
  orderForm.details.length = 0;
};

// 删除确认对话框状态
const deleteConfirmVisible = ref<boolean>(false);
const deleteIndex = ref<number>(-1);

// 显示删除确认对话框
const showDeleteConfirm = (index: number): void => {
  deleteIndex.value = index;
  deleteConfirmVisible.value = true;
};

// 确认删除订单明细
const confirmRemoveDetail = (): void => {
  if (deleteIndex.value >= 0) {
    const materialCode = orderForm.details[deleteIndex.value].material_code;
    orderForm.details.splice(deleteIndex.value, 1);

    // 重新分配项次
    orderForm.details.forEach((detail, idx) => {
      detail.order_item = idx + 1;
    });

    // 同时从选中集合中移除
    selectedMaterials.value.delete(materialCode);

    // 关闭确认对话框
    deleteConfirmVisible.value = false;
    deleteIndex.value = -1;
  }
};

// 取消删除
const cancelRemoveDetail = (): void => {
  deleteConfirmVisible.value = false;
  deleteIndex.value = -1;
};

// 处理键盘事件
const handleDeleteKeydown = (event: KeyboardEvent): void => {
  if (event.key === 'Enter' && deleteConfirmVisible.value) {
    // 回车键确认删除
    confirmRemoveDetail();
  } else if (event.key === 'Escape' && deleteConfirmVisible.value) {
    // ESC键取消删除
    cancelRemoveDetail();
  }
};

// 添加和移除键盘事件监听器
const addDeleteKeyListener = (): void => {
  document.addEventListener('keydown', handleDeleteKeydown);
};

const removeDeleteKeyListener = (): void => {
  document.removeEventListener('keydown', handleDeleteKeydown);
};

// 监听deleteConfirmVisible的变化
watch(deleteConfirmVisible, (newValue: boolean) => {
  if (newValue) {
    // 当确认对话框显示时，添加键盘事件监听器
    addDeleteKeyListener();
  } else {
    // 当确认对话框隐藏时，移除键盘事件监听器
    removeDeleteKeyListener();
  }
});

// 删除订单明细（已废弃，改用showDeleteConfirm）
const removeDetail = (index: number): void => {
  showDeleteConfirm(index);
};

// 更新明细价格计算
const updateDetailPrice = (index: number): void => {
  const detail = orderForm.details[index];

  // 确保数值有效
  detail.quantity = parseFloat(String(detail.quantity)) || 0;
  detail.unit_price = parseFloat(String(detail.unit_price)) || 0;
  detail.tax_rate = parseFloat(String(detail.tax_rate)) || 0;

  // 计算金额（不在这里同步生产数量）
  calculateDetailAmounts(detail);
};

// 计算订单明细的各种金额
const calculateDetailAmounts = (detail: OrderDetailItem): void => {
  const quantity = parseFloat(String(detail.quantity)) || 0;
  const unitPrice = parseFloat(String(detail.unit_price)) || 0;
  const taxRate = parseFloat(String(detail.tax_rate)) || 0;
  const taxCategory = orderForm.tax_category || 'B'; // 默认含税

  switch (taxCategory) {
    case 'A': // 零税
      // 零税情况下，税前金额 = 含税金额，税额 = 0
      detail.total_price_no_tax = quantity * unitPrice;
      detail.total_price_tax = detail.total_price_no_tax;
      detail.tax_amount = 0;
      break;

    case 'B': // 单价含税
      // 含税单价，需要从含税金额反推税前金额
      detail.total_price_tax = quantity * unitPrice;
      detail.total_price_no_tax = detail.total_price_tax / (1 + taxRate / 100);
      detail.tax_amount = detail.total_price_tax - detail.total_price_no_tax;
      break;

    case 'C': // 单价不含税
      // 不含税单价，需要计算含税金额
      detail.total_price_no_tax = quantity * unitPrice;
      detail.tax_amount = detail.total_price_no_tax * (taxRate / 100);
      detail.total_price_tax = detail.total_price_no_tax + detail.tax_amount;
      break;

    default:
      // 默认按含税处理
      detail.total_price_tax = quantity * unitPrice;
      detail.total_price_no_tax = detail.total_price_tax / (1 + taxRate / 100);
      detail.tax_amount = detail.total_price_tax - detail.total_price_no_tax;
      break;
  }
};

// 计算订单明细总价（保持向后兼容）
const calculateTotalPrice = (detail: OrderDetailItem): number => {
  return detail.total_price_tax;
};

// 计算订单总金额
const calculateOrderTotal = (): number => {
  return orderForm.details.reduce((total, detail) => {
    return total + calculateTotalPrice(detail);
  }, 0);
};

// 设置预计交货日期为当前日期+30天
const setDefaultDates = (): void => {
  const today = new Date();
  const deliveryDate = new Date();
  deliveryDate.setDate(today.getDate() + 30);

  // 为所有明细设置预计交货日期
  orderForm.details.forEach(detail => {
    if (!detail.expected_delivery_date) {
      detail.expected_delivery_date = deliveryDate.toISOString().split('T')[0];
    }
  });
};

// 保存订单
const saveOrder = async (): Promise<void> => {
  // 首先验证表单
  if (!validateForm()) {
    // 如果有错误，滚动到第一个错误位置
    const firstError = Object.keys(errors.value)[0];
    const element = document.getElementById(firstError);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
    return;
  }

  loading.value = true;

  // 设置预计交货日期
  setDefaultDates();

  try {
    // 准备提交数据，确保包含税率和税种分类
    const submitData = {
      ...orderForm,
      tax_rate: orderForm.tax_rate,
      tax_category: orderForm.tax_category,
      details: orderForm.details.map(detail => ({
        ...detail,
        tax_rate: detail.tax_rate || orderForm.tax_rate
      }))
    };

    const response = await apiService.post('sales/orders/create', submitData);

    if (response.data.success) {
      // 创建成功，跳转到订单列表页
      router.visit('/sales/orders', {
        onSuccess: () => {
          alert('订单创建成功');
        }
      });
    } else {
      errors.value = response.data.errors || { general: response.data.message };
    }
  } catch (error: any) {
    console.error('创建订单失败', error);

    if (error.response && error.response.data && error.response.data.errors) {
      errors.value = error.response.data.errors;
    } else {
      errors.value = { general: '创建订单失败，请稍后重试' };
    }
  } finally {
    loading.value = false;
  }
};

// 点击外部关闭下拉菜单
const handleClickOutside = (event: MouseEvent): void => {
  const target = event.target as HTMLElement;
  if (showCustomerDropdown.value && !target.closest('#customer-dropdown')) {
    showCustomerDropdown.value = false;
  }
};

// 处理订购数量失去焦点事件
const handleQuantityBlur = (index: number): void => {
  const detail = orderForm.details[index];

  // 确保数值有效
  detail.quantity = parseFloat(String(detail.quantity)) || 0;

  // 同步生产数量逻辑：如果生产数量为0，则同步为订购数量；如果不为0，生产数量不变
  if (detail.production_quantity === 0) {
    detail.production_quantity = detail.quantity;
  }

  // 重新计算金额
  calculateDetailAmounts(detail);
};

onMounted(async () => {
  await loadFormData();
  document.addEventListener('click', handleClickOutside);

  // 如果已经选择了美元币种，则获取汇率
  if (orderForm.currency_code === 'USD') {
    await updateExchangeRate();
  }
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<template>
  <AppLayout title="创建销售订单">
    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
      <div class="flex flex-col space-y-6">
        <!-- 页头 -->
        <div class="flex justify-between items-center">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">创建销售订单</h2>
            <button
            @click="saveOrder"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-300 flex items-center"
          >
            创建订单
          </button>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
          <form @submit.prevent="saveOrder">
            <!-- 订单基本信息表单 -->
            <div class="grid grid-cols-1 md:grid-cols-6 gap-6 mb-6">
              <!-- 第一行：客户、订单日期、业务部门、业务员、销售类型 -->
              <div class="md:col-span-2">
                <InputLabel for="customer_code" :value="'客户' + (orderForm.customer_code ? ' (' + orderForm.customer_code + ')' : '')" required />
                <div class="relative" id="customer-dropdown">
                  <button
                    type="button"
                    @click="showCustomerDropdown = !showCustomerDropdown"
                    class="mt-1 block w-full border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-300 p-2.5 rounded-lg focus:ring-indigo-500 dark:focus:ring-indigo-600 focus:border-indigo-500 dark:focus:border-indigo-600 flex justify-between items-center"
                  >
                    <span class="truncate">
                      {{ orderForm.customer_code ?
                        (formData.customers.find(c => c.customer_code === orderForm.customer_code)?.customer_name || '已选择客户') :
                        '请选择客户' }}
                    </span>
                    <svg class="w-4 h-4 ml-2" :class="{'transform rotate-180': showCustomerDropdown}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                  </button>

                  <!-- 客户下拉选择框 -->
                  <div
                    v-if="showCustomerDropdown"
                    class="absolute z-50 mt-1 bg-white rounded-md shadow-lg dark:bg-gray-700 w-full overflow-hidden"
                    style="min-width: 250px; max-height: 70vh;"
                  >
                    <div class="p-2 border-b border-gray-200 dark:border-gray-600">
                      <span class="text-sm font-medium text-gray-700 dark:text-gray-300">选择客户</span>
                    </div>

                    <div class="sticky top-0 z-10 bg-white dark:bg-gray-700 p-2 border-b border-gray-200 dark:border-gray-600">
                      <input
                        type="text"
                        v-model="customerSearch"
                        placeholder="搜索客户..."
                        class="w-full px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-sm text-gray-900 dark:text-white"
                        @click.stop
                        @keydown="handleCustomerSearchKeydown"
                      />
                    </div>

                    <div class="overflow-y-auto" style="max-height: calc(70vh - 90px);">
                      <div class="p-2">
                        <div
                          v-for="customer in filteredCustomers"
                          :key="customer.customer_code"
                          class="flex items-center p-2 hover:bg-gray-100 dark:hover:bg-gray-600 rounded cursor-pointer"
                          @click="orderForm.customer_code = customer.customer_code; showCustomerDropdown = false; handleCustomerChange();"
                        >
                          <span class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300 truncate">
                            {{ customer.customer_name }}
                          </span>
                        </div>
                        <div v-if="filteredCustomers.length === 0" class="p-2 text-sm text-gray-500 dark:text-gray-400 text-center">
                          暂无匹配客户
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <InputError :message="errors.customer_code" />
              </div>

              <div class="md:col-span-1">
                <InputLabel for="order_date" value="订单日期" required />
                <TextInput
                  id="order_date"
                  type="date"
                  class="mt-1 block w-full"
                  v-model="orderForm.order_date"
                  required
                />
                <InputError :message="errors.order_date" />
              </div>

              <div class="md:col-span-1">
                <InputLabel for="department_id" :value="'业务部门' + (orderForm.department_id ? ' (' + orderForm.department_id + ')' : '')" required />
                <TextInput
                  id="department_id"
                  type="text"
                  class="mt-1 block w-full bg-gray-100"
                  :value="formData.departments.find(d => d.id === orderForm.department_id)?.name || ''"
                  readonly
                />
                <input type="hidden" v-model="orderForm.department_id" />
                <InputError :message="errors.department_id" />
              </div>

              <div class="md:col-span-1">
                <InputLabel for="sales_account" :value="'业务员' + (orderForm.sales_account ? ' (' + orderForm.sales_account + ')' : '')" required />
                <TextInput
                  id="sales_account"
                  type="text"
                  class="mt-1 block w-full bg-gray-100"
                  :value="formData.salesPersons.find(s => s.account === orderForm.sales_account)?.name || ''"
                  readonly
                />
                <input type="hidden" v-model="orderForm.sales_account" />
                <InputError :message="errors.sales_account" />
              </div>

              <div class="md:col-span-1">
                <InputLabel for="sales_type_code" value="销售类型" required />
                <select
                  id="sales_type_code"
                  v-model="orderForm.sales_type_code"
                  class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                  required
                >
                  <option value="">请选择销售类型</option>
                  <option v-for="salesType in formData.salesTypes" :key="salesType.sales_type_code" :value="salesType.sales_type_code">
                    {{ salesType.sales_type_name }}
                  </option>
                </select>
                <InputError :message="errors.sales_type_code" />
              </div>

              <!-- 第二行：币种、汇率、汇率基准、税种 -->
              <div class="md:col-span-1">
                <InputLabel for="currency_code" :value="'币种' + (orderForm.currency_code ? ' (' + orderForm.currency_code + ')' : '')" required />
                <select
                  id="currency_code"
                  v-model="orderForm.currency_code"
                  class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                  required
                  @change="() => { updateExchangeRate(); }"
                >
                  <option value="">请选择币种</option>
                  <option v-for="currency in formData.currencies" :key="currency.currency_code" :value="currency.currency_code">
                    {{ currency.currency_name }}
                  </option>
                </select>
                <InputError :message="errors.currency_code" />
              </div>

              <div class="md:col-span-1">
                <InputLabel for="exchange_rate" value="汇率" required />
                <TextInput
                  id="exchange_rate"
                  type="number"
                  step="0.0001"
                  class="mt-1 block w-full"
                  :model-value="orderForm.exchange_rate.toFixed(6)"
                  @update:model-value="val => orderForm.exchange_rate = parseFloat(val) || 0"
                  required
                />
                <InputError :message="errors.exchange_rate" />
              </div>

              <div class="md:col-span-1">
                <InputLabel for="exchange_rate_base_code" value="汇率基准" required />
                <select
                  id="exchange_rate_base_code"
                  v-model.number="orderForm.exchange_rate_base_code"
                  class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                  required
                >
                  <option :value="0">请选择汇率基准</option>
                  <option v-for="base in formData.exchangeRateBases" :key="base.exchange_rate_base_code" :value="base.exchange_rate_base_code">
                    {{ base.exchange_rate_base_name }}
                  </option>
                </select>
                <InputError :message="errors.exchange_rate_base_code" />
              </div>

              <div class="md:col-span-1">
                <InputLabel for="tax_type_code" :value="'税种' + (orderForm.tax_type_code ? ' (' + orderForm.tax_type_code + ')' : '')" required />
                <select
                  id="tax_type_code"
                  v-model="orderForm.tax_type_code"
                  class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                  required
                  @change="handleTaxTypeChange"
                >
                  <option value="">请选择税种</option>
                  <option v-for="taxType in formData.taxTypes" :key="taxType.tax_type_code" :value="taxType.tax_type_code">
                    {{ taxType.tax_type_name }}({{ taxType.tax_rate }}%) - {{ taxType.tax_category === 'A' ? '零税' : taxType.tax_category === 'B' ? '含税' : '不含税' }}
                  </option>
                </select>
                <InputError :message="errors.tax_type_code" />
              </div>

              <!-- 第三行：发票类型、取价方式、交易条件、收款条件 -->
              <div class="md:col-span-1">
                <InputLabel for="invoice_type_code" :value="'发票类型' + (orderForm.invoice_type_code ? ' (' + orderForm.invoice_type_code + ')' : '')" required />
                <select
                  id="invoice_type_code"
                  v-model="orderForm.invoice_type_code"
                  class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                  required
                >
                  <option value="">请选择发票类型</option>
                  <option v-for="type in formData.invoiceTypes" :key="type.invoice_type_code" :value="type.invoice_type_code">
                    {{ type.invoice_type_name }}
                  </option>
                </select>
                <InputError :message="errors.invoice_type_code" />
              </div>

              <div class="md:col-span-1">
                <InputLabel for="pricing_method_code" value="取价方式" />
                <select
                  id="pricing_method_code"
                  v-model="orderForm.pricing_method_code"
                  class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm bg-gray-100"
                  disabled
                >
                  <option value="">请选择取价方式</option>
                  <option v-for="method in formData.pricingMethods" :key="method.sales_pricing_method_code" :value="method.sales_pricing_method_code">
                    {{ method.sales_pricing_method_name }}
                  </option>
                </select>
                <InputError :message="errors.pricing_method_code" />
              </div>

              <div class="md:col-span-1">
                <InputLabel for="trade_term_code" :value="'交易条件' + (orderForm.trade_term_code ? ' (' + orderForm.trade_term_code + ')' : '')" required />
                <select
                  id="trade_term_code"
                  v-model="orderForm.trade_term_code"
                  class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                  required
                >
                  <option value="">请选择交易条件</option>
                  <option v-for="term in formData.tradeTerms" :key="term.trade_term_code" :value="term.trade_term_code">
                    {{ term.trade_term_name }}
                  </option>
                </select>
                <InputError :message="errors.trade_term_code" />
              </div>

              <div class="md:col-span-1">
                <InputLabel for="receipt_payment_term_code" :value="'收款条件' + (orderForm.receipt_payment_term_code ? ' (' + orderForm.receipt_payment_term_code + ')' : '')" required />
                <select
                  id="receipt_payment_term_code"
                  v-model="orderForm.receipt_payment_term_code"
                  class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                  required
                >
                  <option value="">请选择收款条件</option>
                  <option v-for="term in formData.paymentTerms" :key="term.receipt_payment_term_code" :value="term.receipt_payment_term_code">
                    {{ term.receipt_payment_term_name }}
                  </option>
                </select>
                <InputError :message="errors.receipt_payment_term_code" />
              </div>

              <div class="md:col-span-1">
                <InputLabel for="customer_order_number" value="客户订单号" />
                <TextInput
                  id="customer_order_number"
                  type="text"
                  class="mt-1 block w-full"
                  v-model="orderForm.customer_order_number"
                  placeholder="请输入客户订单号"
                />
                <InputError :message="errors.customer_order_number" />
              </div>
            </div>

            <!-- 备注信息 -->
            <div class="mb-6">
              <div>
                <InputLabel for="remark" value="备注" />
                <textarea
                  id="remark"
                  v-model="orderForm.remark"
                  class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                  rows="4"
                  placeholder="请输入备注信息"
                ></textarea>
              </div>
            </div>

            <!-- 订单明细 -->
            <div class="mb-6">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">订单明细</h3>
                <div class="flex items-center space-x-3">
                  <PrimaryButton type="button" @click="openMaterialDialog">添加物料</PrimaryButton>
                </div>
              </div>

              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-16">项次</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-20">行状态</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-32">料号</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-40">品名</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-32">规格</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-32">客户料号</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-36">客户料号规格</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-16">单位</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-24">订购数量</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-24">生产数量</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-28">单价</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-36">预计完工日期</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-36">预订交货日期</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-20">税率(%)</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-28">税前金额</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-28">含税金额</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-24">税额</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-28">备注</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-28">清表编号</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-40">商标</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-48">说明书</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-48">颜色</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-40">内包装</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-40">外包装</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-40">警告</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-24">借用</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-64">长备注</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-20">操作</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <tr v-for="(detail, index) in orderForm.details" :key="index">
                      <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 w-16">{{ detail.order_item }}</td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 w-20">
                        <span class="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 rounded">一般</span>
                      </td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 w-32">{{ detail.material_code }}</td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 w-40">{{ detail.material_name }}</td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 w-32">{{ detail.material_spec }}</td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 w-32">
                        <TextInput
                          type="text"
                          class="w-28"
                          v-model="detail.customer_material_code"
                          placeholder="客户料号"
                        />
                      </td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 w-36">
                        <TextInput
                          type="text"
                          class="w-32"
                          v-model="detail.customer_material_specification"
                          placeholder="客户料号规格"
                        />
                      </td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 w-16">
                        {{ detail.unit }}
                      </td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 w-24">
                        <input
                          type="text"
                          inputmode="numeric"
                          pattern="[0-9]*"
                          class="w-20 border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                          :value="String(detail.quantity)"
                          @input="(e) => { detail.quantity = parseFloat((e.target as HTMLInputElement).value) || 0; updateDetailPrice(index); }"
                          @blur="handleQuantityBlur(index)"
                          min="1"
                          style="appearance: textfield !important; -moz-appearance: textfield !important; -webkit-appearance: textfield !important;"
                        />
                      </td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 w-24">
                        <input
                          type="text"
                          inputmode="numeric"
                          pattern="[0-9]*"
                          class="w-20 border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                          :value="String(detail.production_quantity)"
                          @input="(e) => { detail.production_quantity = parseFloat((e.target as HTMLInputElement).value) || 0; }"
                          min="0"
                          style="appearance: textfield !important; -moz-appearance: textfield !important; -webkit-appearance: textfield !important;"
                        />
                      </td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 w-28">
                        <input
                          type="text"
                          inputmode="decimal"
                          pattern="[0-9]*\.?[0-9]*"
                          class="w-24 border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                          :value="detail.unit_price.toFixed(4)"
                          @input="(e) => { detail.unit_price = parseFloat((e.target as HTMLInputElement).value) || 0; updateDetailPrice(index); }"
                          style="appearance: textfield !important; -moz-appearance: textfield !important; -webkit-appearance: textfield !important;"
                        />
                      </td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 w-36">
                        <TextInput
                          type="date"
                          class="w-32"
                          v-model="detail.expected_completion_date"
                        />
                      </td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 w-36">
                        <TextInput
                          type="date"
                          class="w-32"
                          v-model="detail.expected_delivery_date"
                        />
                      </td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 w-20">
                        <span class="text-sm">{{ detail.tax_rate }}%</span>
                      </td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 w-28">
                        {{ detail.total_price_no_tax.toFixed(4) }}
                      </td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 w-28">
                        {{ detail.total_price_tax.toFixed(4) }}
                      </td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 w-24">
                        {{ detail.tax_amount.toFixed(4) }}
                      </td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 w-28">
                        <TextInput
                          type="text"
                          class="w-24"
                          v-model="detail.remark"
                          placeholder="备注"
                        />
                      </td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 w-28">
                        <TextInput
                          type="text"
                          class="w-24"
                          v-model="detail.clear_table_number"
                          placeholder="清表编号"
                        />
                      </td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 w-40">
                        <TextInput
                          type="text"
                          class="w-40"
                          v-model="detail.brand"
                          placeholder="商标"
                        />
                      </td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 w-48">
                        <TextInput
                          type="text"
                          class="w-48"
                          v-model="detail.description"
                          placeholder="说明书"
                        />
                      </td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 w-40">
                        <TextInput
                          type="text"
                          class="w-40"
                          v-model="detail.color"
                          placeholder="颜色"
                        />
                      </td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 w-40">
                        <TextInput
                          type="text"
                          class="w-40"
                          v-model="detail.inner_packing"
                          placeholder="内包装"
                        />
                      </td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 w-40">
                        <TextInput
                          type="text"
                          class="w-40"
                          v-model="detail.outer_packing"
                          placeholder="外包装"
                        />
                      </td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 w-40">
                        <TextInput
                          type="text"
                          class="w-40"
                          v-model="detail.warning"
                          placeholder="警告"
                        />
                      </td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 w-24">
                        <TextInput
                          type="text"
                          class="w-20"
                          v-model="detail.borrow"
                          placeholder="借用"
                        />
                      </td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 w-64">
                        <TextInput
                          type="text"
                          class="w-64"
                          v-model="detail.remark_2"
                          placeholder="长备注"
                        />
                      </td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm font-medium w-20">
                        <button type="button" @click="showDeleteConfirm(index)" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                          删除
                        </button>
                      </td>
                    </tr>
                    <tr v-if="orderForm.details.length === 0">
                      <td colspan="25" class="px-4 py-2 text-center text-gray-500 dark:text-gray-400">
                        请添加订单明细
                      </td>
                    </tr>
                    <tr v-if="orderForm.details.length > 0" class="bg-gray-50 dark:bg-gray-700">
                      <td colspan="8" class="px-2 py-2 text-right font-medium">合计:</td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm font-bold text-gray-900 dark:text-gray-100">
                        {{ orderForm.details.reduce((sum, detail) => sum + (parseFloat(String(detail.quantity)) || 0), 0).toFixed(0) }}
                      </td>
                      <td colspan="5" class="px-2 py-2"></td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm font-bold text-gray-900 dark:text-gray-100">
                        {{ orderForm.details.reduce((sum, detail) => sum + detail.total_price_no_tax, 0).toFixed(4) }}
                      </td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm font-bold text-gray-900 dark:text-gray-100">
                        {{ orderTotal.toFixed(4) }} {{ orderForm.currency_code }}
                      </td>
                      <td class="px-2 py-2 whitespace-nowrap text-sm font-bold text-gray-900 dark:text-gray-100">
                        {{ orderForm.details.reduce((sum, detail) => sum + detail.tax_amount, 0).toFixed(4) }}
                      </td>
                      <td colspan="8"></td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <InputError :message="errors.details" />
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 物料选择对话框 -->
    <DialogModal :show="materialDialogVisible" max-width="7xl" :closeable="false">
      <template #title>
        选择物料
        <span v-if="selectedMaterials.size > 0" class="ml-2 text-sm text-gray-500">
          (已选择 {{ selectedMaterials.size }} 项)
        </span>
      </template>
      <template #content>
        <div class="mt-4 flex gap-6">
          <!-- 左侧：物料搜索和列表 -->
          <div class="flex-1">
            <div class="flex items-center mb-4">
              <TextInput
                type="search"
                placeholder="搜索物料编码、名称或规格"
                class="flex-1 mr-2"
                v-model="materialSearch"
              />
              <button
                type="button"
                @click="searchMaterials"
                class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
              >
                搜索
              </button>
            </div>

            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">状态</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">编码</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">名称</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">规格</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">单位</th>
                  </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                  <tr
                    v-for="material in sortedMaterialList"
                    :key="material.material_code"
                    class="hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer"
                    :class="{ 'bg-blue-50 dark:bg-blue-900/20': isMaterialSelected(material.material_code) }"
                    @click="toggleMaterialSelection(material)"
                  >
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                      <div class="flex items-center">
                        <input
                          type="checkbox"
                          :checked="isMaterialSelected(material.material_code)"
                          @click.stop="toggleMaterialSelection(material)"
                          class="rounded border-gray-300 dark:border-gray-700 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                        />
                        <span v-if="isMaterialSelected(material.material_code)" class="ml-2 text-xs text-green-600 dark:text-green-400">
                          ✓ 已选
                        </span>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">{{ material.material_code }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ material.product_name }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">{{ material.specification }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">{{ material.unit }}</td>
                  </tr>
                  <tr v-if="sortedMaterialList.length === 0">
                    <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                      {{ materialSearch ? '没有找到匹配的物料，请修改搜索条件' : '请输入搜索条件查找物料' }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div v-if="sortedMaterialList.length > 0" class="flex justify-between items-center mt-4">
              <div class="text-sm text-gray-700 dark:text-gray-300">
                显示 {{ sortedMaterialList.length }} 条，共 {{ materialTotal }} 条
              </div>
              <div class="flex space-x-2">
                <button
                  type="button"
                  @click="prevMaterialPage"
                  :disabled="materialPage === 1"
                  class="px-3 py-1 bg-gray-200 dark:bg-gray-700 rounded-md disabled:opacity-50"
                >
                  上一页
                </button>
                <button
                  type="button"
                  @click="nextMaterialPage"
                  :disabled="materialPage >= Math.ceil(materialTotal / materialPerPage)"
                  class="px-3 py-1 bg-gray-200 dark:bg-gray-700 rounded-md disabled:opacity-50"
                >
                  下一页
                </button>
              </div>
            </div>
          </div>

          <!-- 右侧：已选中物料清单 -->
          <div class="w-80 border-l border-gray-200 dark:border-gray-700 pl-6">
            <div class="sticky top-0 bg-white dark:bg-gray-800">
              <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                已选择物料 ({{ selectedMaterials.size }})
              </h4>

              <div v-if="selectedMaterials.size === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                <p class="mt-2 text-sm">暂未选择任何物料</p>
              </div>

              <div v-else class="space-y-2 max-h-96 overflow-y-auto">
                <div
                  v-for="materialCode in Array.from(selectedMaterials)"
                  :key="materialCode"
                  class="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800"
                >
                  <div class="flex-1 min-w-0">
                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                      {{ getSelectedMaterialName(materialCode) }}
                    </div>
                    <div class="text-xs text-gray-500 dark:text-gray-400 truncate">
                      {{ materialCode }}
                    </div>
                  </div>
                  <button
                    type="button"
                    @click="removeSelectedMaterial(materialCode)"
                    class="ml-2 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                    title="移除"
                  >
                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>

              <div v-if="selectedMaterials.size > 0" class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <button
                  type="button"
                  @click="clearAllSelectedMaterials"
                  class="w-full px-3 py-2 text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 border border-red-300 dark:border-red-600 rounded-md hover:bg-red-50 dark:hover:bg-red-900/20"
                >
                  清空所有选择
                </button>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template #footer>
        <div class="flex justify-between items-center w-full">
          <div class="text-sm text-gray-600 dark:text-gray-400">
            <span v-if="selectedMaterials.size > 0">
              已选择 {{ selectedMaterials.size }} 项物料，点击确定添加到订单
            </span>
          </div>
          <div class="flex space-x-3">
            <SecondaryButton @click="closeMaterialDialog">
              取消
            </SecondaryButton>
            <PrimaryButton
              @click="addSelectedMaterials"
              :disabled="selectedMaterials.size === 0"
            >
              确定添加 ({{ selectedMaterials.size }})
            </PrimaryButton>
          </div>
        </div>
      </template>
    </DialogModal>

    <!-- 删除确认对话框 -->
    <div v-if="deleteConfirmVisible" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- 背景遮罩 -->
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" @click="cancelRemoveDetail"></div>

        <!-- 定位弹框到订单明细位置 -->
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
             :style="deleteIndex >= 0 && orderForm.details[deleteIndex] ?
                    { position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)' } :
                    {}">
          <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900 sm:mx-0 sm:h-10 sm:w-10">
                <svg class="h-6 w-6 text-red-600 dark:text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100" id="modal-title">
                  确认删除
                </h3>
                <div class="mt-2">
                  <p class="text-sm text-gray-500 dark:text-gray-400">
                    您确定要删除这条订单明细吗？此操作无法撤销。
                  </p>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    <span class="inline-flex items-center">
                      <span class="px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded text-gray-700 dark:text-gray-300 font-mono">Enter</span>
                      <span class="ml-1">确认删除</span>
                    </span>
                    <span class="inline-flex items-center ml-3">
                      <span class="px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded text-gray-700 dark:text-gray-300 font-mono">Esc</span>
                      <span class="ml-1">取消</span>
                    </span>
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
              @click="confirmRemoveDetail"
            >
              确认删除
            </button>
            <button
              type="button"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              @click="cancelRemoveDetail"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>


<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.highlight-row {
  background-color: rgba(79, 70, 229, 0.1) !important;
}

/* 彻底隐藏数字输入框的增减按钮 */
input[type="number"],
:deep(input[type="number"]) {
  -moz-appearance: textfield !important;
  appearance: textfield !important;
  -webkit-appearance: textfield !important;
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button,
:deep(input[type="number"]::-webkit-outer-spin-button),
:deep(input[type="number"]::-webkit-inner-spin-button) {
  -webkit-appearance: none !important;
  appearance: none !important;
  margin: 0 !important;
  display: none !important;
  opacity: 0 !important;
  width: 0 !important;
  height: 0 !important;
  pointer-events: none !important;
}

/* 通用样式应用于所有数字输入框 */
:deep(.no-spinner) input {
  -moz-appearance: textfield !important;
  -webkit-appearance: textfield !important;
  appearance: textfield !important;
}

/* 全局样式覆盖 */
:global(input[type="number"]) {
  -moz-appearance: textfield !important;
  -webkit-appearance: textfield !important;
  appearance: textfield !important;
}

:global(input[type="number"]::-webkit-outer-spin-button),
:global(input[type="number"]::-webkit-inner-spin-button) {
  -webkit-appearance: none !important;
  appearance: none !important;
  margin: 0 !important;
  display: none !important;
}
</style>